import { ListItemButton, ListItemText, ListItemIcon, Typography } from '@mui/material'
import isEqual from 'lodash/fp/isEqual'
import { useNavigate, useLocation } from 'react-router'

import { URL_PATH_DASHBOARD } from 'constants'
const NavItem = ({ item, setCurrentPage }) => {
  const location = useLocation()
  const navigate = useNavigate()

  const handleClick = () => {
    setCurrentPage(item.title)
    // eslint-disable-next-line no-undef
    localStorage.setItem('currentPage', item.title)
    navigate(item.path)
  }

  const isSelected = () => {
    const isRoot = location.pathname === '/'
    return isEqual(item.path, location.pathname) || (isRoot && isEqual(item.path, URL_PATH_DASHBOARD))
  }

  return (
    <ListItemButton
      sx={{
        borderRadius: '8px',
        mb: 0.5,
        alignItems: 'flex-start',
        backgroundColor: 'inherit',
        py: 1,
        pl: '24px',
        fontSize: '1rem',
        '&.Mui-selected .MuiTypography-root': {
          fontWeight: 500
        }
      }}
      selected={isSelected()}
      onClick={handleClick}
    >
      <ListItemIcon sx={{ my: 'auto', minWidth: 36 }}>{item.icon}</ListItemIcon>
      <ListItemText
        primary={
          <Typography variant='body1' color='inherit'>
            {item.title}
          </Typography>
        }
      />
    </ListItemButton>
  )
}

export default NavItem
