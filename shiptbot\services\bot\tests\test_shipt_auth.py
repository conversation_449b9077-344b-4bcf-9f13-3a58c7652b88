import pydash
from shiptbot.services.bot.shipt_auth import <PERSON><PERSON><PERSON><PERSON>


def test_shipt_auth():
    shipt_auth = ShiptAuth()

    username = "<EMAIL>"
    password = "Conc@c123"

    token_data = shipt_auth.create_oauth_token(username, password)
    access_token = pydash.get(token_data, "access_token")
    assert access_token is not None

    mfa_channels = shipt_auth.get_mfa_channels(access_token)
    assert mfa_channels is not None

    channel_id = pydash.get(mfa_channels, "channels[0].id")
    assert channel_id is not None

    shipt_auth.send_mfa_code(access_token, channel_id)
