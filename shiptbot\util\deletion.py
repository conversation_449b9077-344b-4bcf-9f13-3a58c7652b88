import logging
from typing import List

from django.db import ProgrammingError, models
from django.db.models.deletion import CASCADE

logger = logging.getLogger(__name__)


class PseudoDeletionException(RuntimeError):
    def __init__(self, msg):
        self.message = msg
        super().__init__()


class PseudoDeletionDryRunException(RuntimeError):
    def __init__(self, msg, data=None):
        self.message = msg
        self.data = data
        super().__init__()


class PseudoDeletable(models.Model):
    """Any class inheriting this will be able to call "pseudo_delete". This method
    will delete all related objects except for those white-listed in the class-level
    list RETAIN_ON_PSEUDO_DELETE. When deleted, is_deleted will be set to True and
    the field named in PREPEND_DELETED_ON_FIELD will be prepended with [DELETED]. If no
    PREPEND_DELETED_ON_FIELD is set, nothing is renamed.

    Only related objects with CASCADE relation with the parent model will be deleted.
    Other relations will be skipped.
    """

    RETAIN_ON_PSEUDO_DELETE = []
    PREPEND_DELETED_ON_FIELD = None

    is_deleted = models.Bo<PERSON>anField(default=False, blank=False, null=False)

    class Meta:
        abstract = True

    def pseudo_delete(
        self, dry_run=False, additional_retained_models: List[str] = [], **kwargs
    ) -> dict:
        """Delete all (non-cascade) related models except those named in
        RETAIN_ON_PSEUDO_DELETE and relabel to prepend [DELETED] to the
        field specified by PREPEND_DELETED_ON_FIELD.

        Args:
            dry_run: prohibits any actual change but gives the same result dict
                as if it had run
            ADDITIONAL_RETAINED_MODELS protects those models through
                recursive calls of pseudo_delete that are expected
                to be protected via parent classes.

        returns:
            (dict) deletion record
        """

        # Skip deleting if explicitly retained either in this model or
        # parent model(s) calling this recursively
        retained_models = list(
            {*self.RETAIN_ON_PSEUDO_DELETE, *additional_retained_models}
        )

        # Rename model to remark its deletion status
        obj_label = "N/A"
        if self.PREPEND_DELETED_ON_FIELD is not None:
            try:
                current_val: str = getattr(self, self.PREPEND_DELETED_ON_FIELD)
                obj_label = current_val
                if not current_val.startswith("[DELETED]") and not dry_run:
                    setattr(
                        self, self.PREPEND_DELETED_ON_FIELD, f"[DELETED] {current_val}"
                    )
            except AttributeError:
                pass

        if not dry_run:
            self.is_deleted = True
            self.save()

        deletion_map = dict(
            label=obj_label,
            deleted=[],
            failed_deletions=[],
        )

        # Delete m2m/fk relations. If none are to be retained, then all will be deleted
        for rel_obj in self._meta.related_objects:
            rm = rel_obj.related_model
            rm_label = rm._meta.label
            rm_rel_classname = rel_obj.__class__.__name__

            # This would be something eg 'auditentry_set' queryset or the related obj
            accessor_name = rel_obj.get_accessor_name()

            if rm_label in retained_models:
                continue

            # Skip deleting. Require deletion to normally CASCADE.
            if rel_obj.on_delete != CASCADE:
                logger.debug(
                    f"Skip deletion of related models {rm_label} "
                    f"found through field named '{accessor_name}' on "
                    f"{self!r} as the relation does not CASCADE on delete."
                )
                continue

            try:
                accessor_field = getattr(self, accessor_name)
            except Exception as e:
                logger.error(
                    f"Failed to delete related models {rm_label} "
                    f"found through field named '{accessor_name}' on "
                    f"{self!r}: {str(e)}"
                )
                continue

            # If we can, try to pseudo-delete. Otherwise REALLY delete them.
            # This could be optimized to recognize pdeletability earlier at expense of
            # readability. We only want to delete o2m or m2m relations
            deletables = None
            if accessor_field is not None:
                if rel_obj.multiple:
                    deletables = accessor_field.all()
                    if not deletables.exists():
                        deletables = None

            if not deletables:
                continue

            rm_is_pdeletable = issubclass(rm, PseudoDeletable)
            count = deletables.count()
            if not dry_run and rm_is_pdeletable:
                for instance in deletables:
                    try:
                        instance.pseudo_delete(
                            additional_retained_models=retained_models
                        )
                    except PseudoDeletionException as pde:
                        logger.error(
                            f"Failed to pseduodelete {count} related instance(s) of "
                            f"{rm_label}' for {self!r}: {pde}"
                        )
                        deletion_map["failed_deletions"].append(
                            dict(
                                label=rm_label,
                                pseudodelete=rm_is_pdeletable,
                                exception=str(pde),
                            )
                        )
                        continue
            elif not dry_run and not rm_is_pdeletable:
                try:
                    deletables.delete()
                except (models.ObjectDoesNotExist, ProgrammingError) as e:
                    logger.error(
                        f"Failed to delete {count} related instance(s) of "
                        f"{rm_label}' for {self!r}: one or more child objects may"
                        f" not exist. {e}"
                    )
                    deletion_map["failed_deletions"].append(
                        dict(
                            label=rm_label,
                            pseudodelete=rm_is_pdeletable,
                            exception=str(e),
                        )
                    )
                    continue

            deletion_map["deleted"].append(
                dict(
                    object_type=rm_label,
                    relation_type=rm_rel_classname,
                    accessor_name=accessor_name,
                    pseudo_deleted=rm_is_pdeletable,
                    count=count,
                )
            )

            logger.debug(
                f"Deleted {count} related instance(s) of " f"{rm_label}' for {self!r}"
            )

        return deletion_map
