import json
from unittest.mock import Mock, patch

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from shiptbot.models.user.models import User


class Auth0ShiptTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        self.url = reverse("auth0_shipt")

    def test_missing_user_id(self):
        """Test that missing user_id returns 400"""
        data = {
            "data": {
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token"
            }
        }
        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("user_id is required", response.data["error"])
        self.assertIn("Missing required field: user_id", response.data["message"])

    def test_missing_access_token(self):
        """Test that missing access_token returns 400"""
        data = {
            "data": {
                "user_id": str(self.user.id),
                "refresh_token": "test_refresh_token"
            }
        }
        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("access_token is required", response.data["error"])
        self.assertIn("Missing required field: access_token", response.data["message"])

    def test_missing_refresh_token(self):
        """Test that missing refresh_token returns 400"""
        data = {
            "data": {
                "user_id": str(self.user.id),
                "access_token": "test_access_token"
            }
        }
        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("refresh_token is required", response.data["error"])
        self.assertIn("Missing required field: refresh_token", response.data["message"])

    def test_user_not_found(self):
        """Test that non-existent user returns 404"""
        data = {
            "data": {
                "user_id": "********-0000-0000-0000-************",
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token"
            }
        }
        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("User not found", response.data["error"])
        self.assertIn("No user found with ID:", response.data["message"])

    @patch('shiptbot.services.bot.shipt_bot.ShiptBot.get_account_info')
    def test_failed_account_info_returns_406(self, mock_get_account_info):
        """Test that failed account info retrieval returns 406"""
        # Mock the get_account_info to raise an exception
        mock_get_account_info.side_effect = Exception("API Error")
        
        data = {
            "data": {
                "user_id": str(self.user.id),
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token"
            }
        }
        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_406_NOT_ACCEPTABLE)
        self.assertIn("Failed to get account info", response.data["error"])

    @patch('shiptbot.services.bot.shipt_bot.ShiptBot.get_account_info')
    def test_account_info_no_id_returns_406(self, mock_get_account_info):
        """Test that account info without id returns 406"""
        # Mock the get_account_info to return data without id
        mock_get_account_info.return_value = {"name": "Test User"}
        
        data = {
            "data": {
                "user_id": str(self.user.id),
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token"
            }
        }
        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_406_NOT_ACCEPTABLE)
        self.assertIn("Failed to get account info", response.data["error"])

    @patch('shiptbot.services.bot.shipt_bot.ShiptBot.get_account_info')
    def test_successful_token_save(self, mock_get_account_info):
        """Test successful token save"""
        # Mock the get_account_info to return valid data
        mock_get_account_info.return_value = {"id": 12345, "name": "Test User"}
        
        data = {
            "data": {
                "user_id": str(self.user.id),
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token"
            }
        }
        response = self.client.post(self.url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Tokens saved successfully", response.data["message"])
        
        # Verify tokens were saved
        self.user.refresh_from_db()
        self.assertEqual(self.user.shipt_access_token, "test_access_token")
        self.assertEqual(self.user.shipt_refresh_token, "test_refresh_token")
