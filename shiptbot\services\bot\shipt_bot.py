import logging
import re
import time

import arrow
import pydash
import requests
from django.conf import settings

from shiptbot.models.bot.models import OFFER_STATUS, Offer, Store

from .constants import OFFER_HEADERS_BASE
from .shipt_auth import ShiptAuth

logger = logging.getLogger(__name__)


class ShiptBot:
    def __init__(self, user, session=None):
        self.user = user
        self.headers = OFFER_HEADERS_BASE.copy()
        self.headers["x-shipt-geo-long"] = "-122.68234219244364"
        self.headers["x-shipt-geo-lat"] = "45.56178265826245"
        self.headers["x-shipt-geo-last-tracked"] = arrow.utcnow().isoformat()

        self.shipt_access_token = self.user.shipt_access_token
        self.setting = self.user.botsetting
        self.enabled_stores = self.user.store_set.filter(enabled=True)
        self.session = session

        self.accepted_offer_ids = {}
        self.processed_offer_ids = {}

    def retry_request(self, url):
        shipt_auth = ShiptAuth(self.user)
        token_data = shipt_auth.refresh_token()
        self.shipt_access_token = pydash.get(token_data, "access_token")

        response = requests.get(url, headers=self.headers_with_auth)
        return response

    @property
    def headers_with_auth(self):
        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {self.shipt_access_token}"
        return headers

    def get_account_info(self):
        url = "https://shopper-api.shipt.com/shopper-bff/account/v1/shopper/info"
        response = requests.get(url, headers=self.headers_with_auth)
        if response.status_code == 401:
            response = self.retry_request(url)

        if response.status_code != 200:
            logger.warning(
                f"Failed to get account info. Status code: {response.status_code}",
                extra=dict(response=response.text),
            )
            raise Exception(f"Failed to get account info. Status code: {response.status_code}")

        try:
            return response.json()
        except Exception as e:
            logger.warning(f"Failed to parse account info response: {str(e)}")
            raise Exception("Failed to parse account info response")

    def get_zones(self):
        url = "https://shopper-api.shipt.com/shopper-bff/schedule/v1/zones?isGeo=false"

        response = requests.get(url, headers=self.headers_with_auth)
        if response.status_code == 401:
            response = self.retry_request(url)

        if response.status_code != 200:
            logger.warning(
                f"Failed to get zones. Status code: {response.status_code}",
                extra=dict(response=response.text),
            )

        return response.json()

    def refresh_zones_and_stores(self):
        """
        Update the current list of stores and zones in the database.

        Behavior:
        - If a store from the response does not exist in the database, it will be created.
        - If a store exists in both the response and the database, it will be updated.
        - If a store exists in the database but not in the response, it will be removed.

        Sample Data:
        [
            {
                "zone_id": "1710",
                "zone_name": "Beaverton",
                "store_locations": [
                    {
                        "id": "15293",
                        "store": "Petco",
                        "name": "Petco - 117th Ave",
                        "street": "4037 Sw 117th Avenue",
                        "city": "Beaverton",
                        "state": "OR",
                        "zip": "97005",
                        "image_url": "https://object-storage.shipt.com/stores/PetcoCircle_0606d1f1f1ccfe963e9d19e0e6cceabb.png",
                        "coordinates": {
                            "latitude": 45.4902018,
                            "longitude": -122.798379
                        }
                    },
                    {
                        "id": "19754",
                        "store": "Office Depot OfficeMax",
                        "name": "Office Depot - SW Cedar Hills Blvd",
                        "street": "3485 SW Cedar Hills Blvd",
                        "city": "Beaverton",
                        "state": "OR",
                        "zip": "97005",
                        "image_url": "https://object-storage.shipt.com/stores/OfficeDepotOfficeMax_82dc55a5b7d6293df0aa0630c939a3ba.png",
                        "coordinates": {
                            "latitude": 45.4951112,
                            "longitude": -122.8117757
                        }
                    }
                ]
            }
        ]
        """
        updated_store_ids = []
        zones = self.get_zones()
        for zone in pydash.get(zones, "current_zones", []):
            zone_id = pydash.get(zone, "zone_id")
            zone_name = pydash.get(zone, "zone_name")
            zone_obj, created = self.user.zone_set.get_or_create(zone_id=zone_id)
            zone_obj.zone_name = zone_name
            zone_obj.save()

            for store in pydash.get(zone, "store_locations", default=[]):
                store_id = pydash.get(store, "id")
                store_name = pydash.get(store, "name")
                store_data = store

                store_obj, created = self.user.store_set.get_or_create(
                    store_id=store_id, zone=zone_obj
                )

                store_obj.store_name = store_name
                store_obj.store_data = store_data
                store_obj.save()
                updated_store_ids.append(store_id)

        # Remove stores that are no longer in the response
        self.user.store_set.exclude(store_id__in=updated_store_ids).delete()

    def get_open_metro_offers(self):
        url = "https://shopper-api.shipt.com/shopper-bff/offering/v1/offers/open-metro"
        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {self.shipt_access_token}"

        response = requests.get(url, headers=headers)
        if response.status_code == 401:
            response = self.retry_request(url)

        if response.status_code != 200:
            logger.warning(
                f"Failed to get open metro offers. Status code: {response.status_code}",
                extra=dict(response=response.text),
            )

        return response.json()

    def get_offers(self):
        url = "https://shopper-api.shipt.com/shopper-bff/offering/v1/offers"
        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {self.shipt_access_token}"

        response = requests.get(url, headers=headers)
        if response.status_code == 401:
            response = self.retry_request(url)

        if response.status_code != 200:
            logger.warning(
                f"Failed to get offers. Status code: {response.status_code}",
                extra=dict(response=response.text),
            )
            self.user.shipt_is_running = False
            self.user.save()

        return response.json()

    def get_offer_detail(self, order_bundle_id):
        url = f"https://shopper-api.shipt.com/shopper-bff/offering/v1/offers/{order_bundle_id}/card-view"
        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {self.shipt_access_token}"

        response = requests.get(url, headers=headers)
        if response.status_code == 401:
            response = self.retry_request(url, headers)

        if response.status_code != 200:
            logger.warning(
                f"Failed to get offer detail. Status code: {response.status_code}",
                extra=dict(response=response.text),
            )

        return response.json()

    def claim_offer(self, offer_data):
        if settings.FAKE_CLAIM_OFFER:
            self.accepted_offer_ids[offer_data["order_bundle_id"]] = True
            self._create_offer(offer_data, status=OFFER_STATUS.ACCEPTED)
            return

        bff_claim_url = offer_data["bff_claim_url"]
        url = f"https://shopper-api.shipt.com/shopper-bff/{bff_claim_url}"
        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {self.shipt_access_token}"

        response = requests.patch(url, headers=headers)
        if response.status_code == 401:
            response = self.retry_request(url, headers)

        if response.status_code != 200:
            logger.warning(
                f"Failed to claim offer. Status code: {response.status_code}",
                extra=dict(response=response.text),
            )

        self.accepted_offer_ids[offer_data["order_bundle_id"]] = True
        self._create_offer(offer_data, status=OFFER_STATUS.ACCEPTED)

    def _stop(self):
        self.session.ended_at = arrow.utcnow().datetime
        self.session.save()
        self.user.shipt_is_running = False
        self.user.save()

    def start_seaching(self):
        while self.user.shipt_is_running:
            print("================== Searching for offers ======================")
            self._search()
            self.user.refresh_from_db()

        self._stop()

    def _search(self):
        data = self.get_open_metro_offers()
        print(data, "data")
        for offer in data["open_metro"]:
            self._process_offer(offer)

        time.sleep(self.setting.refresh_rate_seconds)

    def _process_offer(self, offer_data):
        order_bundle_id = offer_data["order_bundle_id"]
        if order_bundle_id in self.processed_offer_ids:
            return
        else:
            self.processed_offer_ids[order_bundle_id] = True

        offer_detail = self.get_offer_detail(order_bundle_id)
        should_take, skip_reason = self._validate_offer(offer_detail)
        if should_take:
            pass
        else:
            self._create_offer(offer_detail, skip_reason=skip_reason)

    def _create_offer(self, offer_data, status=OFFER_STATUS.SKIPPED, skip_reason=None):
        offer = Offer.objects.create(
            user=self.user,
            offer_data=offer_data,
            status=status,
            skip_reason=skip_reason,
            session=self.session,
        )
        return offer

    def _validate_offer(self, offer_data):
        should_take = True
        skip_reason = None

        store_id = pydash.get(offer_data, "orders[0].tracked_properties.store_id")
        try:
            store = self.enabled_stores.get(store_id=store_id)
        except Store.DoesNotExist:
            should_take = False
            skip_reason = "Store not enabled"
            return should_take, skip_reason

        if store.min_pay:
            est_pay = offer_data["estimated_shopper_pay"]
            if est_pay < store.min_pay:
                should_take = False
                skip_reason = f"Pay of ${est_pay} is below min of ${store.min_pay}"
                return should_take, skip_reason

        est_distance = self._extract_est_mile(offer_data["subheader"])
        if store.min_distance_miles and est_distance:
            if est_distance < store.min_distance_miles:
                should_take = False
                skip_reason = f"Distance of {est_distance} is below min of {store.min_distance_miles}"
                return should_take, skip_reason

        if store.max_distance_miles and est_distance:
            if est_distance > store.max_distance_miles:
                should_take = False
                skip_reason = f"Distance of {est_distance} is above max of {store.max_distance_miles}"
                return should_take, skip_reason

        if store.min_pay_per_mile and est_distance:
            est_pay_per_mile = est_pay / est_distance
            if est_pay_per_mile < store.min_pay_per_mile:
                should_take = False
                skip_reason = f"Pay per mile of ${est_pay_per_mile} is below min of ${store.min_pay_per_mile}"
                return should_take, skip_reason

        return should_take, skip_reason

    def _extract_est_mile(self, subheader):
        match = re.search(r"([\d.]+)\s*mi", subheader)

        if match:
            distance = float(match.group(1))
            return distance

        return None
