import { SelectElement } from 'react-hook-form-mui'

const SelectFieldSmart = ({ name, data, errors, control, ...otherProps }) => {
  const required = { ...otherProps }.rules.required
  const options = data.enum.map((item) => ({
    id: item,
    label: item
  }))

  return (
    <SelectElement
      name={name}
      label={`${data.title}${required ? ' *' : ''}`}
      options={options}
      control={control}
      margin='normal'
      fullWidth
      error={!!errors[name]}
      helperText={errors[name] ? errors[name].message : ''}
      {...otherProps}
    />
  )
}

export default SelectFieldSmart
