import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar
} from '@mui/material'
import { styled } from '@mui/material/styles'
import AccessTimeIcon from '@mui/icons-material/AccessTime'
import LocationOnIcon from '@mui/icons-material/LocationOn'
import DriveEtaIcon from '@mui/icons-material/DriveEta'
import StorefrontIcon from '@mui/icons-material/Storefront'
import { OFFER_STATUS } from '../../../constants/backend'

const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: 16,
  boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
  border: '1px solid #f0f0f0',
  transition: 'all 0.3s ease-in-out',
  overflow: 'hidden',
  '&:hover': {
    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
    transform: 'translateY(-4px)',
    borderColor: '#e0e0e0'
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: 12,
    marginBottom: theme.spacing(1.5)
  }
}))

const StoreAvatar = styled(Avatar)(({ theme }) => ({
  width: 48,
  height: 48,
  backgroundColor: '#cc0000',
  fontSize: '1.2rem',
  fontWeight: 'bold'
}))

const PriceBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  color: '#2e7d32',
  fontWeight: 'bold'
}))

const InfoRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  color: '#666',
  fontSize: '0.875rem',
  marginBottom: theme.spacing(0.5)
}))

const StatusChip = styled(Chip)(({ status, theme }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'open': return { bg: '#e8f5e8', color: '#2e7d32' }
      case 'accepted': return { bg: '#e3f2fd', color: '#1976d2' }
      case 'missed': return { bg: '#ffebee', color: '#d32f2f' }
      case 'completed': return { bg: '#f3e5f5', color: '#7b1fa2' }
      default: return { bg: '#f5f5f5', color: '#666' }
    }
  }

  const colors = getStatusColor()

  return {
    backgroundColor: colors.bg,
    color: colors.color,
    fontWeight: 600,
    fontSize: '0.75rem'
  }
})

const OfferCard = ({ offer }) => {
  // Extract data with fallbacks
  const storeName = offer.store_name || 'Store'
  const storeLocation = offer.store_location_name || 'Location'
  const estimatedPay = offer.est_shopper_pay || '0.00'
  const orderStatus = offer.order_status || null
  const offerStatus = offer.status || null
  const timeslot = offer.timeslot_info
  const zoneName = offer.zone_name || 'Zone'
  const shopTime = offer.est_shop_time || 0
  const driveTime = offer.est_drive_time || 0
  const skipReason = offer.skip_reason
  const promoPay = offer.promo_pay && offer.promo_pay / 100

  // Format status
  const getStatusLabel = (status) => {
    if (status === 'open') return 'Metro Open'
    return status?.charAt(0).toUpperCase() + status?.slice(1)
  }

  // Format offer status
  const getOfferStatusInfo = (status) => {
    switch (status) {
      case OFFER_STATUS.ACCEPTED:
        return { label: 'Accepted', color: '#e3f2fd', textColor: '#1976d2' }
      case OFFER_STATUS.GONE:
        return { label: 'Gone', color: '#ffebee', textColor: '#d32f2f' }
      case OFFER_STATUS.SKIPPED:
        return { label: 'Skipped', color: '#fff3e0', textColor: '#f57c00' }
      default:
        return null
    }
  }

  // Helper functions
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(parseFloat(amount || 0))
  }

  const formatTime = (timeString) => {
    if (!timeString) return 'N/A'
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (timeString) => {
    if (!timeString) return 'N/A'
    return new Date(timeString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  const formatTimeMinutes = (minutes) => {
    if (!minutes || minutes === 0) return '0m'
    const roundedMinutes = Math.round(minutes)
    if (roundedMinutes < 60) {
      return `${roundedMinutes}m`
    } else {
      const hours = Math.floor(roundedMinutes / 60)
      const mins = roundedMinutes % 60
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
    }
  }

  const truncateOrderId = (id, maxLength = 15) => {
    if (!id) return 'N/A'
    if (id.length <= maxLength) return id

    // Show first part and last part with ... in between
    const startLength = Math.floor((maxLength - 3) / 2) // Reserve 3 chars for "..."
    const endLength = maxLength - 3 - startLength
    return `${id.substring(0, startLength)}...${id.substring(id.length - endLength)}`
  }

  return (
    <StyledCard>
      <CardContent sx={{
        p: { xs: 2, sm: 3 },
        '&:last-child': { pb: { xs: 2, sm: 3 } }
      }}
      >
        {/* Header with store info and pay */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          mb: 0.5
        }}
        >
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 1, sm: 1.5 },
            flex: 1,
            minWidth: 0
          }}
          >
            <Box sx={{ minWidth: 0, flex: 1 }}>
              <Typography
                variant='h6'
                sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  lineHeight: 1.2
                }}
              >
                {storeName}
              </Typography>
              <Typography
                variant='body2'
                color='text.secondary'
                sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
              >
                {storeLocation}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ textAlign: 'right' }}>
            <Typography
              variant='h5'
              sx={{
                fontWeight: 'bold',
                color: '#2e7d32',
                fontSize: { xs: '1.25rem', sm: '1.5rem' }
              }}
            >
              {formatCurrency(estimatedPay)}
            </Typography>
            <Typography
              variant='caption'
              color='text.secondary'
              sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
            >
              Est. Pay
            </Typography>
          </Box>
        </Box>

        {/* Time & Location Info */}
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <InfoRow>
              <AccessTimeIcon sx={{ fontSize: 16 }} />
              <Typography variant='body2'>
                {formatDate(timeslot)} at {formatTime(timeslot)}
              </Typography>
            </InfoRow>

            <InfoRow>
              <StorefrontIcon sx={{ fontSize: 16 }} />
              <Typography variant='body2'>
                Shop: {formatTimeMinutes(shopTime)}
              </Typography>
              <DriveEtaIcon sx={{ fontSize: 16, ml: 1 }} />
              <Typography variant='body2'>
                Drive: {formatTimeMinutes(driveTime)}
              </Typography>
            </InfoRow>

            <InfoRow sx={{ mb: 0 }}>
              <LocationOnIcon sx={{ fontSize: 16 }} />
              <Typography variant='body2'>
                {zoneName}
              </Typography>
            </InfoRow>
          </Box>
          {promoPay && (
            <Box sx={{ textAlign: 'right' }}>
              <Typography
                variant='h5'
                sx={{
                  fontWeight: 'bold',
                  color: '#ff9800',
                  fontSize: { xs: '1rem', sm: '1.25rem' }
                }}
              >
                {formatCurrency(promoPay)}
              </Typography>
              <Typography
                variant='caption'
                color='text.secondary'
                sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
              >
                Promo Pay
              </Typography>
            </Box>
          )}
        </Box>

        {/* Status Badges */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: { xs: 'wrap', sm: 'nowrap' },
          gap: { xs: 1, sm: 0 }
        }}
        >
          <Box sx={{
            display: 'flex',
            gap: { xs: 0.5, sm: 1 },
            flexWrap: 'wrap'
          }}
          >
            <StatusChip
              label={getStatusLabel(orderStatus)}
              status={orderStatus}
              size='small'
            />
            {offerStatus && getOfferStatusInfo(offerStatus) && (
              <Chip
                label={getOfferStatusInfo(offerStatus).label}
                size='small'
                sx={{
                  backgroundColor: getOfferStatusInfo(offerStatus).color,
                  color: getOfferStatusInfo(offerStatus).textColor,
                  fontSize: { xs: '0.7rem', sm: '0.75rem' },
                  fontWeight: 600,
                  height: { xs: 24, sm: 28 }
                }}
              />
            )}
          </Box>

          {skipReason && (
            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
              <Typography
                variant='caption'
                color='text.secondary'
              >
                Reason: {skipReason}
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>
    </StyledCard>
  )
}

export default OfferCard
