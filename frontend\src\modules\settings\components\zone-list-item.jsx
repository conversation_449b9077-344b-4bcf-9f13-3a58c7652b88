import React, { useState } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Collapse,
  IconButton,
  Box,
  Divider
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import ExpandLessIcon from '@mui/icons-material/ExpandLess'
import { styled } from '@mui/material/styles'

import StoreCard from './store-card'

const ZoneHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  cursor: 'pointer'
}))

const ZoneListItem = ({ zone }) => {
  const [expanded, setExpanded] = useState(false)

  const handleToggleExpand = () => {
    setExpanded(!expanded)
  }

  const enabledStoresCount = zone.stores.filter(store => store.enabled).length
  const totalStoresCount = zone.stores.length
  const hasEnabledStores = enabledStoresCount > 0

  return (
    <Card
      sx={{
        mb: 2,
        backgroundColor: hasEnabledStores ? 'primary.light' : 'background.paper',
        border: '2px solid',
        borderColor: hasEnabledStores ? 'primary.main' : 'grey.300',
        borderRadius: 2,
        boxShadow: hasEnabledStores
          ? '0 4px 12px rgba(46, 125, 50, 0.15)'
          : '0 2px 8px rgba(0, 0, 0, 0.08)',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          boxShadow: hasEnabledStores
            ? '0 6px 16px rgba(46, 125, 50, 0.2)'
            : '0 4px 12px rgba(0, 0, 0, 0.12)',
          transform: 'translateY(-1px)'
        }
      }}
    >
      <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
        <ZoneHeader onClick={handleToggleExpand}>
          <Box>
            <Typography variant='h5' component='h2'>
              {zone.zone_name}
            </Typography>
            <Typography variant='body2' color='text.secondary'>
              {enabledStoresCount} of {totalStoresCount} stores enabled
            </Typography>
          </Box>
          <IconButton
            onClick={handleToggleExpand}
            aria-expanded={expanded}
            aria-label='show more'
          >
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </ZoneHeader>

        <Collapse in={expanded} timeout='auto' unmountOnExit>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ mt: 2 }}>
            {zone.stores.length > 0
              ? (
                  zone.stores.map((store) => (
                    <StoreCard key={store.id} store={store} />
                  ))
                )
              : (
                <Typography variant='body2' color='text.secondary' sx={{ textAlign: 'center', py: 2 }}>
                  No stores found in this zone
                </Typography>
                )}
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  )
}

export default ZoneListItem
