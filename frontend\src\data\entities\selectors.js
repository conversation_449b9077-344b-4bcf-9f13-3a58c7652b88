import invariant from 'invariant'
import getOr from 'lodash/fp/getOr'
import has from 'lodash/fp/has'
import keys from 'lodash/fp/keys'
import lodashPick from 'lodash/fp/pick'

const hasEntities = has('entities')

export function get (state, type, id, defaultEntity = null) {
  invariant(hasEntities(state), 'Bad state passed to get. Check params.')
  invariant(has(type)(state.entities), `Bad entity type passed to get: ${type}`)

  if (!id && id !== 0) {
    return defaultEntity
  }
  return getOr(defaultEntity, id)(state.entities[type])
}

/**
 * @param state
 * @param type
 * @param ids An array (possible an empty one) of ids to retrieve. If an empty array, return an empty array.
 *  If undefined, return all entities of type.
 * @returns {Array}
 */
export function list (state, type, ids) {
  invariant(hasEntities(state), 'Bad state passed to list. Check params.')
  invariant(has(type)(state.entities), `Bad entity type passed to list: ${type}`)

  let entityList = []
  const entities = state.entities[type]

  if (ids !== undefined && !Array.isArray(ids)) {
    return []
  }

  if (entities) {
    const myIds = ids !== undefined ? ids : keys(entities)
    entityList = myIds.filter(id => has(id, entities)).map(id => entities[id])
  }

  return entityList
}

export function all (state, type) {
  invariant(hasEntities(state), 'Bad state passed to all. Check params.')
  invariant(has(type)(state.entities), `Bad entity type passed to all: ${type}`)

  return Object.values(state.entities[type])
}

export function pick (state, type, ids) {
  invariant(hasEntities(state), 'Bad state passed to pick. Check params.')
  invariant(has(type)(state.entities), `Bad entity type passed to pick: ${type}`)

  return lodashPick(ids)(state.entities[type])
}
