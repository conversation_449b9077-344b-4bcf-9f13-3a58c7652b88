import { ENTITY_TYPES } from 'data/entities/constants'
import { RestResource } from '../helpers/rest-resource'

export class BotSettingsRestResource extends RestResource {
  get defaultOptions () {
    return {
      entities: ENTITY_TYPES.BOTSETTINGS
    }
  }
}

export class ShiptBotRestResource extends RestResource {
  get defaultOptions () {
    return {
      verbs: ['start', 'search']
    }
  }

  start (options = {}) {
    return this.requestThunk({
      method: 'put',
      url: this.buildUrl('/start/'),
      actions: this.createActionCreators('start'),
      options
    })
  }

  stop (options = {}) {
    return this.requestThunk({
      method: 'post',
      url: this.buildUrl('/stop/'),
      actions: this.createActionCreators('stop'),
      options
    })
  }
}
