import { configureStore } from '@reduxjs/toolkit'

import { LOGOUT_REDUX_ACTION, LOGOUT_STORAGE_EVENT } from 'constants/storage'
import createRootReducer from './reducer'

const STORAGE_KEY_AUTH = 'auth'

export const store = configureStore({
  reducer: createRootReducer
})

export function doLogoutAndCleanup () {
  localStorage.setItem(LOGOUT_STORAGE_EVENT, `logout-${Date.now()}`)
  // Clear out persisted data
  localStorage.removeItem(STORAGE_KEY_AUTH)
  // Dispatch the logout action
  store.dispatch({ type: LOGOUT_REDUX_ACTION })
}

// Logout of all tabs and clear persisted state when one tab logs out
window.addEventListener('storage', (event) => {
  if (event.key === LOGOUT_STORAGE_EVENT) {
    // Clear the state tree
    store.dispatch({ type: LOGOUT_REDUX_ACTION })
  }
})
