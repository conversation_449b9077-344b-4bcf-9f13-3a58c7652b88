import { css } from '@emotion/react'

// somewhat arbitrary measures of screen sizes
export const SCREEN_XSMALL_W = 480
export const SCREEN_XSMALL_H = 480
export const SCREEN_SMALL_W = 640
export const SCREEN_TABLET_MIN_W = 768
export const SCREEN_TABLET_MAX_W = 1024
export const SCREEN_MEDIUM_W = 1024
export const SCREEN_LARGE_W = 1280
export const SCREEN_XLARGE_W = 1920
export const CONTAINER_SIGNING_LARGE_W = 1100 // max signing width
export const CUSTOM_MESSAGES_MIN_WIDTH = 1700

// z-index of 1002 fixes an issue where
export const Z_INDEX_SIDEBAR = 1002
export const Z_INDEX_HEADER = 1002

export const mediaSizes = {
  tinyOnly: [0, SCREEN_XSMALL_W],
  smallOnly: [0, SCREEN_SMALL_W],
  // see smallOnlyWidthHeight below as well
  smallUp: SCREEN_XSMALL_W + 1,
  mediumUp: SCREEN_SMALL_W + 1,
  // 768 is a default breakpoint for some semantic ui react components, so mediumAlt is sometimes necessary
  // to be in sync with semantic ui react
  mediumAltUp: 769,
  largeUp: SCREEN_MEDIUM_W + 1,
  xlargeUp: SCREEN_LARGE_W + 1,
  xxlargeUp: SCREEN_XLARGE_W + 1
}

export const media = {}

Object.keys(mediaSizes).forEach((label) => {
  const size = mediaSizes[label]

  if (Array.isArray(size) && size.length === 2) {
    media[label] = (...args) => css`
            @media (min-width: ${size[0]}px)  and (max-width: ${size[1]}px) {
                ${css(...args)}
            }
        `
  } else {
    media[label] = (...args) => css`
            @media (min-width: ${size}px) {
                ${css(...args)}
            }
        `
  }
})

media.smallOnlyWidthHeight = (...args) => css`
    @media (max-width: ${SCREEN_SMALL_W}px), (max-height: ${SCREEN_XSMALL_H}px) {
        ${css(...args)}
    }
`

export const COLORS = {
  AQUA_SUI: '#276F86',
  AQUA_DARK_SUI: '#0E566C',
  BLACK: '#000000',
  BLUE_BLUEINK: '#3553A4',
  BLUE_LIGHT: '#81C6DF',
  BLUE_PALE: '#AAE3C6',
  BLUE_BRIGHT: '#004BAE',
  BLUE_VIVID: '#0014D2',
  BLUE_DARK: '#0073D2',
  BLUE_DARKER: '#293F7D',
  BLUE_DARKERER: '#313172',
  BLUE_DEEP: '#22205F',
  BLUE_DARK_BG: '#182D66',
  BLUE_SUI: '#1E70BF',
  GRAY_20: '#202020',
  GRAY_50: '#505050',
  GRAY_70: '#707070',
  GRAY_77: '#777777',
  GRAY_99: '#999999',
  GRAY_A0: '#A0A0A0',
  GRAY_B0: '#B0B0B0',
  GRAY_CC: '#CCCCCC',
  GRAY_DD: '#DDDDDD',
  GRAY_E4: '#E4E4E4',
  GRAY_F0: '#F0F0F0',
  GRAY_F4: '#F4F4F4',
  GRAY_F5: '#F5F5F5',
  GRAY_F9: '#F9F9F9',
  GRAY_F9A: '#F9FAFB',
  GRAY_SUI: '#222426', // Semantic UI Gray
  GREEN: '#6CAF4B',
  GREEN_BRIGHT: '#86ffc5',
  GREEN_DARK: '#568B3C',
  GREEN_SUI: '#21BA45',
  ORANGE_SUI: '#F2711C',
  PERIWINKLE: '#CCCCFF',
  PERIWINKLE_LIGHT: '#DFDFFF',
  PURPLE: '#8722C7',
  RED_BRIGHT: '#DE2222',
  RED_ERROR: '#D40B0B',
  RED_PINK: '#BB5C56',
  RED_SUI: '#DB2828', // Semantic UI Red
  WHITE: '#FFFFFF',
  YELLOW: '#F1E800',
  YELLOW_PALE: '#F5E04A',
  YELLOW_SUI: '#FBBD08',
  YELLOW_WARNING: '#DFB600',
  GRAY_SUI_MENU_BORDER: '#D4D4D5',
  GRAY_SUI_MENU_ITEM_DISABLED: '#2828284c'
}
