import React from 'react'
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Typography,
  IconButton
} from '@mui/material'
import { styled } from '@mui/material/styles'
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward'
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward'

const SortContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: theme.spacing(2),
  padding: theme.spacing(1.5),
  backgroundColor: 'white',
  borderBottomLeftRadius: 12,
  borderBottomRightRadius: 12,
  border: '1px solid #f0f0f0',
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(1),
    marginBottom: theme.spacing(1.5)
  }
}))

const SortSelect = styled(Select)(({ theme }) => ({
  minWidth: 120,
  '& .MuiOutlinedInput-notchedOutline': {
    border: '1px solid #e0e0e0'
  },
  '& .MuiSelect-select': {
    padding: '8px 12px',
    fontSize: '0.875rem'
  },
  [theme.breakpoints.down('md')]: {
    minWidth: 100,
    '& .MuiSelect-select': {
      padding: '6px 10px',
      fontSize: '0.8rem'
    }
  }
}))

const SortLabel = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  fontWeight: 500,
  color: '#666',
  [theme.breakpoints.down('md')]: {
    fontSize: '0.8rem'
  }
}))

const SORT_OPTIONS = [
  { value: 'created_at', label: 'Found' },
  { value: 'est_shopper_pay', label: 'Pay' },
  { value: 'promo_pay', label: 'Promo Pay' },
  { value: 'est_drive_time', label: 'Drive Time' },
  { value: 'est_shop_time', label: 'Shop Time' }
]

const OffersSort = ({
  sortBy = 'created_at',
  sortOrder = 'desc',
  onSortChange
}) => {
  const handleSortByChange = (event) => {
    const newSortBy = event.target.value
    onSortChange(newSortBy, sortOrder)
  }

  const handleSortOrderToggle = () => {
    const newSortOrder = sortOrder === 'desc' ? 'asc' : 'desc'
    onSortChange(sortBy, newSortOrder)
  }

  const getSortIcon = () => {
    if (sortOrder === 'desc') {
      // High to Low (descending) - ArrowUpward
      return (
        <ArrowUpwardIcon
          sx={{
            fontSize: { xs: 18, md: 20 }
          }}
        />
      )
    } else {
      // Low to High (ascending) - ArrowDownward
      return (
        <ArrowDownwardIcon
          sx={{
            fontSize: { xs: 18, md: 20 }
          }}
        />
      )
    }
  }

  return (
    <SortContainer>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SortLabel>Sort by:</SortLabel>
        <FormControl size='small'>
          <SortSelect
            value={sortBy}
            onChange={handleSortByChange}
            displayEmpty
          >
            {SORT_OPTIONS.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </SortSelect>
        </FormControl>
      </Box>

      <IconButton
        onClick={handleSortOrderToggle}
        size='small'
        sx={{
          border: '1px solid #e0e0e0',
          borderRadius: 1,
          padding: { xs: 0.5, md: 1 },
          '&:hover': {
            backgroundColor: '#f5f5f5'
          }
        }}
        title={sortOrder === 'desc' ? 'High to Low' : 'Low to High'}
      >
        {getSortIcon()}
      </IconButton>
    </SortContainer>
  )
}

export default OffersSort
