import { selectors, ENTITY_TYPES } from 'data/entities/index'

/**
 * Get all stores from the state
 * @param {Object} state - Redux state
 * @returns {Array} Array of store objects
 */
export function getAllStores (state) {
  return selectors.all(state, ENTITY_TYPES.STORES)
}

/**
 * Get stores grouped by zone_id
 * @param {Object} state - Redux state
 * @returns {Object} Object with zone_id as keys and arrays of stores as values
 */
export function getStoresByZone (state) {
  const stores = getAllStores(state)

  return stores.reduce((zones, store) => {
    const zoneId = store.zone_id
    if (!zones[zoneId]) {
      zones[zoneId] = {
        zone_id: zoneId,
        zone_name: store.zone_name,
        stores: []
      }
    }
    zones[zoneId].stores.push(store)
    return zones
  }, {})
}

/**
 * Get a specific store by ID
 * @param {Object} state - Redux state
 * @param {string|number} storeId - Store ID
 * @returns {Object|null} Store object or null if not found
 */
export function getStore (state, storeId) {
  return selectors.get(state, ENTITY_TYPES.STORES, storeId)
}

/**
 * Get stores for a specific zone
 * @param {Object} state - Redux state
 * @param {string} zoneId - Zone ID
 * @returns {Array} Array of stores in the zone
 */
export function getStoresByZoneId (state, zoneId) {
  const stores = getAllStores(state)
  return stores.filter(store => store.zone_id === zoneId)
}
