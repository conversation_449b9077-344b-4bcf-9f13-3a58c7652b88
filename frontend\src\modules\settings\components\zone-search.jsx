import React, { useState, useMemo } from 'react'
import {
  Box,
  TextField,
  InputAdornment,
  Typography,
  Card,
  CardContent,
  Chip,
  IconButton,
  Collapse,
  Divider,
  Button,
  CircularProgress
} from '@mui/material'
import SearchIcon from '@mui/icons-material/Search'
import ClearIcon from '@mui/icons-material/Clear'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import ExpandLessIcon from '@mui/icons-material/ExpandLess'
import { styled } from '@mui/material/styles'
import { connect } from 'react-redux'

import { selectors as storeSelectors } from 'data/stores'
import StoreCard from './store-card'
import API from 'data/api'

const SearchContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3)
}))

const ZoneHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  cursor: 'pointer'
}))

const ZoneSearch = ({ storesByZone, loading, refreshStores }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedZones, setExpandedZones] = useState({})

  const zones = Object.values(storesByZone)

  // Filter zones and stores based on search term
  const filteredZones = useMemo(() => {
    if (!searchTerm.trim()) {
      return zones
    }

    const searchLower = searchTerm.toLowerCase().trim()

    return zones.reduce((filtered, zone) => {
      // Check if zone name matches
      const zoneMatches = zone.zone_name.toLowerCase().includes(searchLower)

      // Filter stores that match the search term
      const matchingStores = zone.stores.filter(store =>
        store.store_name.toLowerCase().includes(searchLower)
      )

      // Include zone if zone name matches or if it has matching stores
      if (zoneMatches || matchingStores.length > 0) {
        filtered.push({
          ...zone,
          stores: zoneMatches ? zone.stores : matchingStores,
          isZoneMatch: zoneMatches,
          matchingStoresCount: matchingStores.length
        })
      }

      return filtered
    }, [])
  }, [zones, searchTerm])

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value)
  }

  const handleClearSearch = () => {
    setSearchTerm('')
  }

  const handleToggleZone = (zoneId) => {
    setExpandedZones(prev => ({
      ...prev,
      [zoneId]: !prev[zoneId]
    }))
  }

  const totalResults = filteredZones.reduce((total, zone) => total + zone.stores.length, 0)

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  if (zones.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Button
          variant='contained'
          size='large'
          onClick={refreshStores}
          sx={{
            mb: 2,
            px: 4,
            py: 1.5
          }}
        >
          Refresh Zones
        </Button>
        <Typography variant='h6' color='text.secondary'>
          No stores found
        </Typography>
        <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
          Please refresh your zones and stores to see available locations.
        </Typography>
      </Box>
    )
  }

  return (
    <Box>
      {/* Header with text and refresh button */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 },
        mb: 3
      }}
      >
        <Typography variant='body1' color='text.secondary' sx={{ flex: 1 }}>
          Search and configure your store preferences for each zone. Enable stores and set minimum pay and distance requirements.
        </Typography>
        <Button
          variant='contained'
          size='large'
          onClick={refreshStores}
          sx={{
            px: 4,
            py: 1.5,
            flexShrink: 0
          }}
        >
          Refresh Zones
        </Button>
      </Box>

      <SearchContainer>
        <TextField
          fullWidth
          placeholder='Search zones or stores...'
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position='start'>
                <SearchIcon color='action' />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position='end'>
                <IconButton
                  size='small'
                  onClick={handleClearSearch}
                  edge='end'
                >
                  <ClearIcon />
                </IconButton>
              </InputAdornment>
            )
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              backgroundColor: 'background.paper',
              '&:hover': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              }
            }
          }}
        />

        {searchTerm && (
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant='body2' color='text.secondary'>
              Found {totalResults} store{totalResults !== 1 ? 's' : ''} in {filteredZones.length} zone{filteredZones.length !== 1 ? 's' : ''}
            </Typography>
            <Chip
              label={`"${searchTerm}"`}
              size='small'
              onDelete={handleClearSearch}
              color='primary'
              variant='outlined'
            />
          </Box>
        )}
      </SearchContainer>

      {filteredZones.length === 0 && searchTerm
        ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant='h6' color='text.secondary'>
              No results found
            </Typography>
            <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
              Try searching with different keywords for zones or stores.
            </Typography>
          </Box>
          )
        : (
          <Box>
            {filteredZones.map((zone) => {
              const isExpanded = expandedZones[zone.zone_id]
              const enabledStoresCount = zone.stores.filter(store => store.enabled).length
              const totalStoresCount = zone.stores.length
              const hasEnabledStores = enabledStoresCount > 0

              return (
                <Card
                  key={zone.zone_id}
                  sx={{
                    mb: 2,
                    backgroundColor: hasEnabledStores ? 'primary.light' : 'background.paper',
                    border: '2px solid',
                    borderColor: hasEnabledStores ? 'primary.main' : 'grey.300',
                    borderRadius: 2,
                    boxShadow: hasEnabledStores
                      ? '0 4px 12px rgba(46, 125, 50, 0.15)'
                      : '0 2px 8px rgba(0, 0, 0, 0.08)',
                    transition: 'all 0.2s ease-in-out'
                  }}
                >
                  <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
                    <ZoneHeader onClick={() => handleToggleZone(zone.zone_id)}>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <Typography variant='h5' component='h2'>
                            {zone.zone_name}
                          </Typography>
                          {zone.isZoneMatch && searchTerm && (
                            <Chip
                              label='Zone match'
                              size='small'
                              color='primary'
                              variant='outlined'
                            />
                          )}
                        </Box>
                        <Typography variant='body2' color='text.secondary'>
                          {enabledStoresCount} of {totalStoresCount} stores enabled
                          {searchTerm && !zone.isZoneMatch && zone.matchingStoresCount > 0 && (
                            <> • {zone.matchingStoresCount} matching store{zone.matchingStoresCount !== 1 ? 's' : ''}</>
                          )}
                        </Typography>
                      </Box>
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation()
                          handleToggleZone(zone.zone_id)
                        }}
                        aria-expanded={isExpanded}
                        aria-label='show more'
                      >
                        {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    </ZoneHeader>

                    <Collapse in={isExpanded} timeout='auto' unmountOnExit>
                      <Divider sx={{ my: 2 }} />
                      <Box sx={{ mt: 2 }}>
                        {zone.stores.length > 0
                          ? (
                              zone.stores.map((store) => (
                                <StoreCard key={store.id} store={store} />
                              ))
                            )
                          : (
                            <Typography variant='body2' color='text.secondary' sx={{ textAlign: 'center', py: 2 }}>
                              No stores found in this zone
                            </Typography>
                            )}
                      </Box>
                    </Collapse>
                  </CardContent>
                </Card>
              )
            })}
          </Box>
          )}
    </Box>
  )
}

const mapStateToProps = (state) => ({
  storesByZone: storeSelectors.getStoresByZone(state),
  loading: false // You can add loading state from your async actions if needed
})

const mapDispatchToProps = dispatch => ({
  refreshStores: () => dispatch(API.stores.refresh())
})

export default connect(mapStateToProps, mapDispatchToProps)(ZoneSearch)
