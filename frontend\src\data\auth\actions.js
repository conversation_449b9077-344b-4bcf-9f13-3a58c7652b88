import { createAction } from 'redux-actions'

import { createAsyncActionCreators } from 'data/helpers/actions'

/**
 * Standard async action creators for auth REST api calls
 * @type {function(*)}
 */
export const authAsyncActions = createAsyncActionCreators('auth')

/**
 * Login a user, with a valid JWT token and user data.
 */
const LOGIN = 'auth/LOGIN'
export const login = createAction(LOGIN, (token, user) => ({ token, user }))

/**
 * Update Auth Status
 */
const UPDATE_AUTH_STATUS = 'auth/UPDATE_AUTH_STATUS'
export const updateAuthStatus = createAction(UPDATE_AUTH_STATUS, (status) => ({ status }))

/**
 * Authentication failed. Note this does not indicate
 * a failed login attempt, but that we received an http response
 * indicating an authentication error (e.g. a 401 response from an API request with a code of FAILED).
 */
const AUTH_FAILED = 'auth/FAILED'
export const authFailed = createAction(AUTH_FAILED, (status = '') => ({ status }))

/**
 * Authentication succeeded. Note this does not indicate
 * a failed login attempt, but that we received an http response
 * indicating an authentication error (e.g. a 401 response from an API request with a code of FAILED).
 */
const AUTH_SUCCESS = 'auth/SUCCESS'
export const authSuccess = createAction(AUTH_SUCCESS)

/**
 * Sets the activeTeam
 */
const SET_ACTIVE_TEAM = 'auth/SET_ACTIVE_TEAM'
export const setActiveTeam = createAction(SET_ACTIVE_TEAM)

/**
 * Sets the User data (a subset of the total User) in auth.
 */
const SET_USER = 'auth/SET_USER'
export const setUser = createAction(SET_USER)
