import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip
} from '@mui/material'
import { styled } from '@mui/material/styles'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import ErrorIcon from '@mui/icons-material/Error'
import StarIcon from '@mui/icons-material/Star'

const StyledCard = styled(Card)(({ theme, isCanceled, isFreeTrail, isExpired }) => ({
  position: 'relative',
  backgroundColor: 'white',
  borderRadius: 20,
  border: isExpired
    ? '2px solid #ff9800'
    : isCanceled
      ? '2px solid #f44336'
      : isFreeTrail
        ? '2px solid #2196f3'
        : '2px solid #4caf50',
  boxShadow: isExpired
    ? '0 8px 32px rgba(255, 152, 0, 0.15)'
    : isCanceled
      ? '0 8px 32px rgba(244, 67, 54, 0.15)'
      : isFreeTrail
        ? '0 8px 32px rgba(33, 150, 243, 0.15)'
        : '0 8px 32px rgba(76, 175, 80, 0.15)',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: isExpired
      ? '0 12px 40px rgba(255, 152, 0, 0.2)'
      : isCanceled
        ? '0 12px 40px rgba(244, 67, 54, 0.2)'
        : isFreeTrail
          ? '0 12px 40px rgba(33, 150, 243, 0.2)'
          : '0 12px 40px rgba(76, 175, 80, 0.2)'
  }
}))

const StatusChip = styled(Chip)(({ status, isFreeTrail, isExpired }) => {
  const getStatusColors = (status, isFreeTrail, isExpired) => {
    if (isExpired) {
      return {
        backgroundColor: '#fff3e0',
        color: '#e65100',
        border: '1px solid #ff9800'
      }
    }

    if (isFreeTrail) {
      return {
        backgroundColor: '#e3f2fd',
        color: '#1565c0',
        border: '1px solid #2196f3'
      }
    }

    switch (status?.toLowerCase()) {
      case 'active':
      case 'trialing':
        return {
          backgroundColor: '#e8f5e8',
          color: '#2e7d32',
          border: '1px solid #4caf50'
        }
      case 'past_due':
      case 'unpaid':
        return {
          backgroundColor: '#fff3e0',
          color: '#e65100',
          border: '1px solid #ff9800'
        }
      case 'canceled':
        return {
          backgroundColor: '#ffebee',
          color: '#c62828',
          border: '1px solid #f44336'
        }
      default:
        return {
          backgroundColor: '#e8f5e8',
          color: '#2e7d32',
          border: '1px solid #4caf50'
        }
    }
  }

  const colors = getStatusColors(status, isFreeTrail, isExpired)

  return {
    ...colors,
    fontWeight: 600,
    fontSize: '0.75rem',
    height: 28,
    '& .MuiChip-icon': {
      color: colors.color,
      fontSize: 16
    }
  }
})

const CurrentPlanCard = ({ subscription }) => {
  // Extract data directly from subscription object (new format)
  const {
    plan_name,
    start_date,
    end_date,
    status_display,
    plan_id,
    is_active,
    period_end,
    period_start
  } = subscription || {}

  // Check if this is a free trial (no period_end, period_start, and plan_id)
  const isFreeTrail = (period_end === null || period_end === undefined) &&
  (period_start === null || period_start === undefined) &&
  (plan_id === null || plan_id === undefined || !plan_id)
  const isCanceled = status_display?.toLowerCase() === 'canceled'

  // Check if subscription is expired
  const isExpired = () => {
    if (isFreeTrail && end_date) {
      // For free trials, check if end_date has passed
      return new Date() > new Date(end_date)
    }
    // For regular subscriptions, check if status is canceled and not active
    return isCanceled && !is_active
  }

  const isSubscriptionExpired = isExpired()

  const formatStatus = () => {
    // Use status_display if available, otherwise use is_active to determine status
    if (status_display) return status_display

    // Fallback based on is_active field
    return is_active ? 'Active' : 'Inactive'
  }

  const formatDate = (dateString) => {
    if (!dateString) return null
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Determine the plan name to display
  const displayPlanName = isFreeTrail ? 'Free Trial' : plan_name

  // Determine the end date to display
  const displayEndDate = isFreeTrail ? end_date : period_end

  return (
    <StyledCard isCanceled={isCanceled} isFreeTrail={isFreeTrail} isExpired={isSubscriptionExpired}>
      <CardContent sx={{ p: { xs: 2.5, sm: 3 }, position: 'relative' }}>
        {/* Header Section */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
          flexWrap: { xs: 'wrap', sm: 'nowrap' },
          gap: { xs: 2, sm: 0 }
        }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {isSubscriptionExpired
              ? (
                <ErrorIcon sx={{
                  fontSize: 28,
                  color: '#ff9800',
                  backgroundColor: '#fff3e0',
                  borderRadius: '50%',
                  p: 0.5
                }}
                />
                )
              : (
                <CheckCircleIcon sx={{
                  fontSize: 28,
                  color: isCanceled ? '#f44336' : isFreeTrail ? '#2196f3' : '#4caf50',
                  backgroundColor: isCanceled ? '#ffebee' : isFreeTrail ? '#e3f2fd' : '#e8f5e8',
                  borderRadius: '50%',
                  p: 0.5
                }}
                />
                )}
            <Box>
              <Typography
                variant='h6' sx={{
                  fontWeight: 700,
                  mb: 0.5,
                  color: '#1a1a1a',
                  fontSize: { xs: '1.1rem', sm: '1.25rem' }
                }}
              >
                Current Plan: {displayPlanName}
              </Typography>
              <Typography
                variant='body2' sx={{
                  color: '#666',
                  fontSize: '0.875rem'
                }}
              >
                {isSubscriptionExpired
                  ? (isFreeTrail ? 'Your free trial has expired' : 'Your subscription has expired')
                  : (isFreeTrail ? 'Your free trial period' : 'Your active subscription')}
              </Typography>
            </Box>
          </Box>

          <StatusChip
            icon={<StarIcon />}
            label={isSubscriptionExpired ? 'Expired' : formatStatus()}
            size='small'
            status={status_display || (is_active ? 'active' : 'inactive')}
            isFreeTrail={isFreeTrail}
            isExpired={isSubscriptionExpired}
          />
        </Box>

        {/* Subscription Dates */}
        {((!isFreeTrail && displayEndDate) || (isFreeTrail && (start_date || displayEndDate))) && (
          <Box sx={{
            pt: 2,
            borderTop: '1px solid #e0e0e0',
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 1, sm: 3 },
            justifyContent: 'space-between'
          }}
          >
            {/* For Free Trial: show start date if available */}
            {isFreeTrail && start_date && (
              <Typography
                variant='body2' sx={{
                  color: '#666',
                  fontSize: '0.875rem'
                }}
              >
                <strong>Trial started:</strong> {formatDate(start_date)}
              </Typography>
            )}

            {/* For subscription plans: don't show start date */}
            {/* For both: show end date with appropriate label */}
            {displayEndDate && (
              <Typography
                variant='body2' sx={{
                  color: '#666',
                  fontSize: '0.875rem'
                }}
              >
                <strong>
                  {isFreeTrail
                    ? 'Trial expires:'
                    : (isCanceled ? 'Expires:' : 'Next billing:')}
                </strong> {formatDate(displayEndDate)}
              </Typography>
            )}
          </Box>
        )}
      </CardContent>
    </StyledCard>
  )
}

export default CurrentPlanCard
