import { RestClient } from './helpers/rest'

import { authAsyncActions } from './auth/actions'
import { shiptAuthAsyncActions } from './shiptauth/actions'
import ShiptAuthRest from './shiptauth/api'

import AuthRest from './auth/api'
import StoresRestResource from './stores/api'
import { storesAsyncActions } from './stores/actions'
import { daysAsyncActions, rangesAsyncActions, dayRangesAsyncActions } from './schedule/actions'
import { DaysRestResource, RangesRestResource, DayRangesRestResource } from './schedule/api'
import OffersRestResource from './offers/api'
import { offersAsyncActions } from './offers/actions'
import { BotSettingsRestResource, ShiptBotRestResource } from './shiptbot/api'
import { botSettingsAsyncActions, shiptBotAsyncActions } from './shiptbot/actions'
import { SubscriptionRestResource } from './subscription/api'
import { subscriptionAsyncActions } from './subscription/actions'
import { PlansRestResource } from './plans/api'
import { plansAsyncActions } from './plans/actions'

const API = new RestClient()

API.register('auth', new AuthRest('/auth', authAsyncActions))
API.register('shiptauth', new ShiptAuthRest('/sauth', shiptAuthAsyncActions))
API.register('botsettings', new BotSettingsRestResource('/botsettings', botSettingsAsyncActions))
API.register('stores', new StoresRestResource('/stores', storesAsyncActions))
API.register('days', new DaysRestResource('/days', daysAsyncActions))
API.register('ranges', new RangesRestResource('/ranges', rangesAsyncActions))
API.register('dayranges', new DayRangesRestResource('/dayranges', dayRangesAsyncActions))
API.register('offers', new OffersRestResource('/offers', offersAsyncActions))
API.register('subscription', new SubscriptionRestResource('/subscription', subscriptionAsyncActions))
API.register('plans', new PlansRestResource('/plans', plansAsyncActions))
API.register('shiptbot', new ShiptBotRestResource('/sb', shiptBotAsyncActions))

export default API
