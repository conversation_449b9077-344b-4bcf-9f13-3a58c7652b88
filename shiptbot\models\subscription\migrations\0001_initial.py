# Generated by Django 5.0.7 on 2025-07-14 04:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import shiptbot.util.buids


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Plan",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "interval",
                    models.CharField(
                        choices=[("mo", "Month"), ("we", "Week")], max_length=10
                    ),
                ),
                ("is_free", models.BooleanField(default=False)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "stripe_price_id",
                    models.CharField(blank=True, max_length=20, null=True, unique=True),
                ),
                ("stripe_upstream_data", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Subscription",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                (
                    "stripe_subscription_id",
                    models.CharField(blank=True, max_length=20, null=True, unique=True),
                ),
                ("stripe_upstream_data", models.JSONField(blank=True, default=dict)),
                (
                    "plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="subscription.plan",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
