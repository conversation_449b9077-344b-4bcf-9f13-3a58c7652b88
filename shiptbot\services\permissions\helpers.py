import logging

logger = logging.getLogger(__name__)


class Perms:
    """PermissionHelper registry. The source of truth about BlueInk permissions."""

    _registry = {}

    @classmethod
    def register(cls, model, permission_class):
        """Register a PermissionHelper class

        Args:
            model: The Model class to associate with the permission_class
            permission_class: a Class derived from PermissionHelperBase

        Returns:
            None

        Raises:
            ValueError if helper_class is not a child class of PermissionHelper, or if
                model is not a Model
            KeyError if model_name is already registered
        """
        if not issubclass(permission_class, PermissionHelperBase):
            raise ValueError(
                "permission_class must be an instance of PermissionHelperBase or a derived class"
            )

        try:
            model_name = model._meta.model_name
        except AttributeError:
            raise ValueError("model does not appear to be a Model class")

        if model_name in cls._registry:
            raise KeyError(
                "Permission Class already registered for model '{}'".format(model_name)
            )

        cls._registry[model_name] = permission_class

    @classmethod
    def get_for_model(cls, model_or_name):
        """Return a new instance of the PermissionHelper class for model_name

        Raises:
            KeyError if there is no registered PermissionHelper class for model_name
        """
        try:
            model_name = model_or_name._meta.model_name
        except AttributeError:
            model_name = model_or_name

        return cls._registry[model_name]()

    @classmethod
    def m(cls, model_or_name):
        """Alias for get_for_model, for the very lazy"""
        return cls.get_for_model(model_or_name)

    # Note: we put these static methods on Perms (and then repeat them on PermissionHelperBase),
    # so that other parts of the codebase can access all of the permission-checks they needs through the Perms class

    @staticmethod
    def is_account_admin(user):
        """True if user is an account admin"""
        try:
            return user.is_account_admin
        except AttributeError:
            return False

    @staticmethod
    def is_account_owner(user):
        """True if user is an account owner"""
        try:
            return user.is_account_owner
        except AttributeError:
            return False

    @staticmethod
    def is_at_least_account_admin(user):
        """True if user is at least an account admin"""
        try:
            return user.is_account_admin or user.is_account_owner
        except AttributeError:
            return False

    @staticmethod
    def is_superuser(user):
        """True if user is a superuser"""
        return user.is_superuser

    @staticmethod
    def is_blueink_staff(user):
        """True if user is a member of BlueInk internal staff"""
        return user.is_staff

    @staticmethod
    def is_blueink_support(user):
        """True if user is a BlueInk internal support user"""
        return user.is_support


class PermissionHelperBase:
    """Helper to unify permissions management and object access inside and outside of Views

    All permission creation and checking in BlueInk should happen via this
    class. This class should be overridden for each model that requires
    permission checks.

    Derived classes from this base class should be registered with Perms, like so:

        Perms.register(SomeModel, SomeModelPermissionHelper)

    By registering with Perms, APIViews that use BlueInkObjectPermissions will
    enforce the permissions implemented by the class.

    All of the can_[view|add|change|delete] methods (and their model permission
    variants ending in _model) return False by default, and must be overridden to
    allow any access.

    Note that model permissions are checked (e.g. can_view_model) before object permissions
    (e.g. can_view). Also object permissions are only checked in DRF views that call
    get_object(). Generic views do object permission checks. View classes derived directly
    from APIView, must call check_object_permissions() themselves.

    Method Naming and Args
        can_xxx: Methods named can_xxx are for checking a permission for a user.
            The first arg to such methods should the User instance, and the next arg (if any)
            is the object being checked. Other args should be named, and come after that. Avoid
            kwargs, as we want to be verbose and explicit regarding what args we examine to
            determine permissions.

        can_obj_xxx: If we want to check if another object can take some action, then
            the other object should part of the method name. E.g. can_person_view_thing,
            or can_system_do_chore

        query_xxx: These methods take a user, other args (optionally), and return a
            queryset of objects of the underlying model. These query methods are
            used by an API View in get_queryset, and in other layers of our architecture.

        query_xxx_for_obj: Query on behalf of a non User, and return a
            queryset of objects of the underlying model. E.g. query_all_for_person.

        list_xxx: Like the query_xxx methods, but these return a list of objects,
            not a queryset.

        list_xxx_for_obj: Like the query_xxx_for_obj methods, but returns a list of objects,
            not a queryset.
    """

    model = None

    class Methods:
        def __init__(self, object_method_name, model_method_name):
            self.obj_method = object_method_name
            self.model_method = model_method_name

    # Maps HTTP verbs to method names on this class. There is
    # an obj_method and a model_method for each HTTP verb, corresponding
    # to an object permissions check and a model permissions check
    PERMISSIONS_MAP = {
        "GET": Methods("can_view", "can_view_model"),
        "OPTIONS": Methods("can_view", "can_view_model"),
        "HEAD": Methods("can_view", "can_view_model"),
        "POST": Methods("can_add", "can_add_model"),
        "PUT": Methods("can_change", "can_change_model"),
        "PATCH": Methods("can_change", "can_change_model"),
        "DELETE": Methods("can_delete", "can_delete_model"),
    }

    def __init__(self):
        self.model = self.get_model()

        if not self.model:
            raise ValueError("Model must be set for PermissionHelper")

    def get_model(self):
        return self.model

    def query_all(self, user=None):
        """Retrieve the list of all objects that this user can view"""
        return self.model.objects.none()

    def can_invoke_method(self, user, http_method, obj=None):
        try:
            methods = self.PERMISSIONS_MAP[http_method]
        except KeyError:
            raise ValueError("Bad http method")

        if obj:
            return getattr(self, methods.obj_method)(user, obj)
        else:
            return getattr(self, methods.model_method)(user)

    ##########################
    # Object permissions tests
    ##########################

    def can_view(self, user, obj):
        return False

    def can_add(self, user, obj):
        # can_add is checked whenever we try to invoke POST on an existing object
        return False

    def can_change(self, user, obj):
        return False

    def can_delete(self, user, obj):
        return False

    #########################
    # Model permissions tests
    #########################

    def can_view_model(self, user):
        return False

    def can_add_model(self, user):
        return False

    def can_change_model(self, user):
        return False

    def can_delete_model(self, user):
        return False

    ##################
    # Test predicates
    ##################

    def get_obj_owner(self, obj):
        """Retrieve the owning User for an object

        Override this if object ownership is not through an 'owner' field on obj,
        for example, if obj points at parent object with an owner
        """
        return obj.owner

    def get_obj_account(self, obj):
        """Retrieve the owning SureAccount for an object

        Override this if object account ownership is not determined through an 'account'
        field on obj
        """
        return obj.account

    def is_obj_owner(self, user, obj):
        """True is user owns the object"""
        obj_owner = self.get_obj_owner(obj)
        return obj_owner == user

    def is_obj_in_account(self, user, obj):
        """True if object is in user's account"""
        obj_account = self.get_obj_account(obj)
        return user.account and obj_account and user.account.id == obj_account.id

    def is_obj_account_admin(self, user, obj):
        """True if user is >= an admin of the account that owns obj"""
        return self.is_obj_in_account(user, obj) and self.is_at_least_account_admin(
            user
        )

    def is_obj_owner_or_account_admin(self, user, obj):
        """True if user owns objects or is >= an admin of the account that owns obj"""
        return self.is_obj_account_admin(user, obj) or self.is_obj_owner(user, obj)

    def is_staff_or_is_obj_in_account(self, user, obj):
        """User is staff, or object belongs to user's account"""
        obj_account = self.get_obj_account(obj)
        return (
            obj_account and user.account and obj_account.id == user.account.id
        ) or self.is_blueink_staff(user)

    @staticmethod
    def is_true():
        return True

    @staticmethod
    def is_false():
        return False

    @staticmethod
    def is_user_the_obj(user, obj):
        """True if user is obj"""
        return user == obj

    @staticmethod
    def is_account_admin(user):
        return Perms.is_account_admin(user)

    @staticmethod
    def is_account_owner(user):
        return Perms.is_account_owner(user)

    @staticmethod
    def is_at_least_account_admin(user):
        return Perms.is_at_least_account_admin(user)

    @staticmethod
    def is_superuser(user):
        return Perms.is_superuser(user)

    @staticmethod
    def is_blueink_staff(user):
        return Perms.is_blueink_staff(user)

    @staticmethod
    def is_blueink_support(user):
        return Perms.is_blueink_support(user)
