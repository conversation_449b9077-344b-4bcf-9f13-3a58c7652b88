import { ENTITY_TYPES } from 'data/entities/constants'
import { RestResource } from 'data/helpers/rest-resource'

class OffersRestResource extends RestResource {
  get defaultOptions () {
    return {
      entities: ENTITY_TYPES.OFFERS
    }
  }

  getOpenMetroOffers (options = {}) {
    return this.requestThunk({
      method: 'get',
      url: this.buildUrl('/get_open_metro_offers/'),
      actions: this.createActionCreators('getOpenMetroOffers'),
      options: {
        ...options,
        noEntities: true // Don't process as entities since this returns raw Shipt API data
      }
    })
  }

  stopSearch (options = {}) {
    return this.requestThunk({
      method: 'post',
      url: this.buildUrl('/stop_search/'),
      actions: this.createActionCreators('stopSearch'),
      options: {
        ...options,
        noEntities: true // Don't process as entities since this returns user data
      }
    })
  }
}

export default OffersRestResource
