import jwt
import pydash
from django.contrib.auth import get_user_model
from django.utils.encoding import smart_str
from django.utils.translation import gettext as _
from rest_framework.authentication import get_authorization_header
from rest_framework_jwt.authentication import JSONWebTokenAuthentication

from shiptbot.services.stytch.helpers import stytch_auth
from shiptbot.util.api_exceptions import BlueinkVaultAPIException
from shiptbot.util.exceptions import AUTH_EXCEPTION


class ShiptBotAuthentication(JSONWebTokenAuthentication):
    """Determine that the request is bearing a valid token and associated with a valid User

    This class controls access to the Dashboard when we are using authentication class (Stytch, ...) for authentication
    (as opposed to using our Django-based JWT authentication)
    """

    www_authenticate_realm = "api"
    auth_helper = stytch_auth

    def authenticate(self, request):
        """
        Returns a two-tuple of `User` and token if a valid signature has been
        supplied using JWT-based authentication.  Otherwise returns `None`.
        """
        jwt_value = self.get_jwt_value(request)

        if jwt_value is None:
            return None

        try:
            payload = self.auth_helper.jwt_decode(jwt_value)
        except jwt.ExpiredSignatureError as e:
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.expired_signature())
        except jwt.DecodeError as e:
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.bad_token())
        except jwt.InvalidTokenError:
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.invalid_token())
        except (RuntimeError, ValueError) as e:
            # Error fetching JWKS from Stytch or matching key from JWKS
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.runtime_error())

        user = self.authenticate_credentials(payload)

        return (user, jwt_value)

    def authenticate_credentials(self, payload):
        """
        Returns an active user that matches the payload's user id and email.
        """
        auth_username = self.auth_helper.get_auth_username_from_payload(payload)

        if not auth_username:
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.invalid_token())

        User = get_user_model()

        try:
            user = self.auth_helper.get_user_from_auth_username(auth_username)
        except User.DoesNotExist:
            user = self.auth_helper.create_user_from_auth_username(auth_username)
            if not user:
                raise BlueinkVaultAPIException(AUTH_EXCEPTION.no_valid_user())

        if not user.is_active:
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.user_is_not_active())

        return user

    def get_jwt_value(self, request):
        auth = get_authorization_header(request).split()
        auth_header_prefix = self.auth_helper.AUTH_HEADER_PREFIX.lower()

        if not auth or (smart_str(auth[0].lower()) != auth_header_prefix):
            return None

        if len(auth) == 1:
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.invalid_header())
        elif len(auth) > 2:
            raise BlueinkVaultAPIException(AUTH_EXCEPTION.header_contain_spaces())

        return auth[1]

    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response, or `None` if the
        authentication scheme should return `403 Permission Denied` responses.
        """
        return '{0} realm="{1}"'.format(
            self.auth_helper.AUTH_HEADER_PREFIX, self.www_authenticate_realm
        )
