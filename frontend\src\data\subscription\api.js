import { RestResource } from 'data/helpers/rest-resource'

import { subscriptionAsyncActions } from './actions'

export class SubscriptionRestResource extends RestResource {
  get defaultOptions () {
    return {
      verbs: ['self', 'checkout', 'billingPortal']
    }
  }

  self (options = {}) {
    return this.requestThunk({
      method: 'get',
      url: this.buildUrl('/self/'),
      actions: subscriptionAsyncActions('self'),
      options
    })
  }

  checkout (data, options = {}) {
    return this.requestThunk({
      method: 'post',
      url: this.buildUrl('/checkout/'),
      actions: subscriptionAsyncActions('checkout'),
      data,
      options
    })
  }

  billingPortal (options = {}) {
    return this.requestThunk({
      method: 'get',
      url: this.buildUrl('/billing_portal/'),
      actions: subscriptionAsyncActions('billingPortal'),
      options
    })
  }
}
