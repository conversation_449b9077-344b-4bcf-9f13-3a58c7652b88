import logging

from django.db import models
from django.db.models.fields.related import ManyTo<PERSON>any<PERSON>ield

from shiptbot.util.buids import (
    DEFAULT_BUID_OBJ_CODE,
    BUIDCode,
    BUIDField,
    generate_buid,
)

from .json import json_dump

logger = logging.getLogger(__name__)


class BasestModel(models.Model):
    BUID_OBJ_CODE = DEFAULT_BUID_OBJ_CODE

    class Meta:
        abstract = True

    id = BUIDField(unique=True, primary_key=True, blank=True, editable=False)

    def save(self, *args, **kwargs):
        """Override this method to set the BUID on save"""
        if not self.id:  # Only generate id if it doesn't already exist
            self.id = generate_buid(obj_code=self.BUID_OBJ_CODE)

        super().save(*args, **kwargs)


class BaseModel(models.Model):
    """
    Integer IDs and UUIDs are not ideal for a couple of reasons:
    - Numerical IDs reveal overall usage information to users
    - UUIDs are ugly in URLs, and not the most efficient encoding
    - Neither of these IDs identify the type of object

    BUIDs consist of an object-type code followed by Base58 encoded unique ID part.
    Base58 is like Base64, but with similar looking characters omitted. It is also URL-friendly.
    See: https://tools.ietf.org/id/draft-msporny-base58-01.html.

    BUIDs are inspired by Stripe's object IDs (and are also used in other popular APIs)

    Example BUIDs

    act_D9qVtXx3xE7E5TdDihXutJ  (for a Account)
    org_8VSXEXVAt               (for an Organization)
    ev_R8H4Q2gFMvExPe           (for an Event)

    Note 1: that the base58 encoded ID part does not always need to be the same length.

    Note 2: base58.b58encode(...) returns bytes. We use the default decoding of "utf-8" to convert
        those bytes to a str. This is almost assuredly what we want because it plays nice with
        Django and Python 3 which use unicode strings.
    """

    class Meta:
        abstract = True

    def __str__(self):
        return "{} {}".format(self._meta.object_name, self.pk)

    def __repr__(self):
        return "<{} {}>".format(self._meta.object_name, self.pk)

    BUID_OBJ_CODE = DEFAULT_BUID_OBJ_CODE

    id = BUIDField(unique=True, primary_key=True, blank=True, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        """Override this method to set the BUID on save"""
        if not self.id:  # Only generate id if it doesn't already exist
            self.id = generate_buid(obj_code=self.BUID_OBJ_CODE)

        super().save(*args, **kwargs)


class ValidatingModel(BasestModel):
    """Model base class that invokes validation via full_clean before saving

    Model.full_clean() calls Model.clean_fields(), Model.clean() and Model.validate_unique().
    So when extending this Model class, you can add validation in the following ways,
    and it will be checked on Model.save()

    1. validators on a Model field, set via the ModelField's 'validators' parameter.
        These validators do not have access to the Model Class or instance, so are
        appropriate for checks that can be performed on the value in isolation.
        For example:

            from django.core.exceptions import ValidationError
            from django.db import models

            def validate_even(value):
                if value % 2 != 0:
                    raise ValidationError("Not even!")

            class MyModel(models.Model):
                even_field = models.IntegerField(validators=[validate_even])

    2. Model.clean(). Appropriate for validation involving multiple fields
       and for providing a value for a field. Also, the right place to check
       for constraints that span to other related Models.
    3. Model.validate_unique(). Validates uniqueness constraints. Only override
       this method to add / modify uniqueness constraints.

    NOTE that we can still get IntegrityError or other errors if the database
    does not like the data we are saving, even if the Model passes our
    custom validation

    NOTE that full_clean() is not invoked on bulk create
    or bulk update operations. For example, these calls will bypass any
    validation invoked in full_clean:

        MyModel.objects.bulk_create() or
        MyModel.objects.filter(foo=x).update(bar=y)

    """

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.full_clean()
        return super().save(*args, **kwargs)


class ValidatingBaseModel(ValidatingModel, BaseModel):
    class Meta:
        abstract = True
