import React, { useEffect } from 'react'
import {
  Card,
  Card<PERSON>ontent,
  Typography,
  Switch,
  FormControlLabel,
  Grid2,
  Box
} from '@mui/material'
import { TextFieldElement, useForm } from 'react-hook-form-mui'
import { connect } from 'react-redux'
import API from 'data/api'
import { selectors as storeSelectors } from 'data/stores'

const StoreCard = ({ store, updateStore }) => {
  const { control, handleSubmit, watch, reset } = useForm({
    defaultValues: {
      enabled: store.enabled,
      min_pay: store.min_pay,
      min_pay_per_mile: store.min_pay_per_mile,
      min_distance_miles: store.min_distance_miles,
      max_distance_miles: store.max_distance_miles
    }
  })

  // Reset form when store data changes (after API update)
  useEffect(() => {
    reset({
      enabled: store.enabled,
      min_pay: store.min_pay,
      min_pay_per_mile: store.min_pay_per_mile,
      min_distance_miles: store.min_distance_miles,
      max_distance_miles: store.max_distance_miles
    })
  }, [store, reset])

  const watchedEnabled = watch('enabled')

  const onSubmit = (data) => {
    updateStore(store.id, data)
  }

  const handleToggleChange = (event) => {
    const newData = {
      enabled: event.target.checked,
      min_pay: watch('min_pay'),
      min_pay_per_mile: watch('min_pay_per_mile'),
      min_distance_miles: watch('min_distance_miles'),
      max_distance_miles: watch('max_distance_miles')
    }
    updateStore(store.id, newData)
  }

  const handleFieldChange = (fieldName, value) => {
    // Update the store immediately when field changes
    const currentValues = {
      enabled: watch('enabled'),
      min_pay: watch('min_pay'),
      min_pay_per_mile: watch('min_pay_per_mile'),
      min_distance_miles: watch('min_distance_miles'),
      max_distance_miles: watch('max_distance_miles')
    }

    const updatedData = {
      ...currentValues,
      [fieldName]: value
    }

    updateStore(store.id, updatedData)
  }

  return (
    <Card sx={{ mb: 1, border: '1px solid #e0e0e0' }}>
      <CardContent sx={{ p: '8px 16px !important' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
          <Typography variant='h5' component='h3'>
            {store.store_name}
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={watchedEnabled}
                onChange={handleToggleChange}
                name='enabled'
              />
            }
            label={watchedEnabled ? 'Enabled' : 'Disabled'}
          />
        </Box>

        {watchedEnabled && (
          <Box component='form' onSubmit={handleSubmit(onSubmit)}>
            <Grid2 container spacing={2}>
              <Grid2 size={6}>
                <TextFieldElement
                  name='min_pay'
                  label='Min Pay ($)'
                  control={control}
                  type='number'
                  inputProps={{ step: '0.01', min: '0' }}
                  fullWidth
                  size='small'
                  onChange={(e) => handleFieldChange('min_pay', e.target.value)}
                />
              </Grid2>
              <Grid2 size={6}>
                <TextFieldElement
                  name='min_pay_per_mile'
                  label='Min Pay Per Mile ($)'
                  control={control}
                  type='number'
                  inputProps={{ step: '0.01', min: '0' }}
                  fullWidth
                  size='small'
                  onChange={(e) => handleFieldChange('min_pay_per_mile', e.target.value)}
                />
              </Grid2>
              <Grid2 size={6}>
                <TextFieldElement
                  name='min_distance_miles'
                  label='Min Distance (miles)'
                  control={control}
                  type='number'
                  inputProps={{ step: '0.1', min: '0' }}
                  fullWidth
                  size='small'
                  onChange={(e) => handleFieldChange('min_distance_miles', e.target.value)}
                />
              </Grid2>
              <Grid2 size={6}>
                <TextFieldElement
                  name='max_distance_miles'
                  label='Max Distance (miles)'
                  control={control}
                  type='number'
                  inputProps={{ step: '0.1', min: '0' }}
                  fullWidth
                  size='small'
                  onChange={(e) => handleFieldChange('max_distance_miles', e.target.value)}
                />
              </Grid2>
            </Grid2>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

const mapStateToProps = (state, ownProps) => {
  // Get the updated store data from Redux store
  const storeFromState = storeSelectors.getAllStores(state).find(s => s.id === ownProps.store.id)
  return {
    store: storeFromState || ownProps.store // Fallback to original prop if not found
  }
}

const mapDispatchToProps = dispatch => ({
  updateStore: (id, data) => dispatch(API.stores.update(id, data))
})

export default connect(mapStateToProps, mapDispatchToProps)(StoreCard)
