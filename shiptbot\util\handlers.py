import logging

import pydash
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import exception_handler

from shiptbot.util.api_exceptions import (
    DEF_EXCEPTION_MESSAGE,
    BaseErrorSchema,
    BlueinkVaultAPIException,
)

logger = logging.getLogger(__name__)


def blueinkvault_exception_handler(exc, context):
    """
    Custom exception handler for BlueinkVault API.

    Args:
        exc (Exception): The exception that was raised.
        context (dict): Additional context about the exception.

    Returns:
        Response: A DRF Response object containing the error details and status code.

    Raises:
        None

    This function uses the `exception_handler` to get the default response for the exception.
    If the exception is an instance of `BlueinkVaultAPIException`, it uses the error schema
    from the exception. If the default response is None, it creates a `BaseErrorSchema` with
    a 500 status code. Otherwise, it creates a `BaseErrorSchema` with details from the exception.

    The error details are logged using the `logger.error` method.
    """
    response = exception_handler(exc, context)

    if isinstance(exc, BlueinkVaultAPIException):
        error_schema = exc.error_schema
    elif response is None:
        error_schema = BaseErrorSchema(
            details=str(exc), status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    else:
        error_schema = BaseErrorSchema(
            status_code=pydash.get(
                exc, "status_code", status.HTTP_500_INTERNAL_SERVER_ERROR
            ),
            message=pydash.get(exc, "message", DEF_EXCEPTION_MESSAGE),
            details=pydash.get(exc, "details", str(exc)),
        )

    logger.error(f"{error_schema.get_error_response}")

    return Response(error_schema.get_error_response, status=error_schema.status_code)
