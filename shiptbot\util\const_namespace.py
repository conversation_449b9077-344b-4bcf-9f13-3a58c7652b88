class ConstStr(str):
    pass


def as_choice(string, label=None):
    s = ConstStr(string)
    s.label = label
    return s


class BaseConstNamespace:
    """Base class for creating Namespaced Constants

    Namespaced Constants are convenient to collect related constants and
    access them via dot notation. This class provides an easy way to define these
    collections of constants.

    Usage:

    class ExampleNamespace(BaseConstNamespace):
        FOO = 'foo'
        BAR = 'bar'
        BAZ = 'baz'

    EN = ExampleNamespace

    assert EN.FOO == 'foo'
    assert EN.BAR = 'bar'

    # ALL is autogenerated, and in alphabetical order by attribute name (not value)
    assert EN.ALL = ['bar', 'baz', 'foo']

    # CHOICES is also auto-generated. See next example
    assert EN.CHOICES = []

    We can also easily configure CHOICES, which can be used in Django models
    for any field that can take a `choices` parameter

    class ExampleNamespaceWithChoices(BaseConstNamespace):
        FOO = as_choice('foo', 'Label for Foo')
        BAR = as_choice('bar', 'Bar Label Here')
        BAZ = 'whatever'  # This will not be included in CHOICES

    EN2 = ExampleNamespaceWithChoices()
    assert EN2.CHOICES = [
        ('bar', "Bar Label Here"),
        ('foo', "Label for Foo")
    ]

    # Deprecated constants should be added to the DEPRECATED attribute
    class ExampleNamespaceWithDeprecated(BaseConstNamespace):
        FOO = as_choice('foo', 'Label for Foo')
        BAR = as_choice('bar', 'Bar Label Here')
        BAZ = 'whatever'
        DEPRECATED = [BAR, BAZ]

    """

    _SPECIAL_NAMES = ("ALL", "CHOICES", "DEPRECATED", "export_to_js", "get_label")
    DEPRECATED = []

    def _get_const_attrs(self):
        const_attribute_names = []
        for attr_name in dir(self):
            if not attr_name.startswith("_") and attr_name not in self._SPECIAL_NAMES:
                const_attribute_names.append(attr_name)

        key_order = self._get_key_in_order()
        const_attribute_names.sort(
            key=lambda x: key_order.index(x) if x in key_order else len(key_order)
        )

        return const_attribute_names

    def _get_choices(self):
        choices = []
        for attr_name in self._get_const_attrs():
            try:
                attr = getattr(self, attr_name)
                choices.append((attr, attr.label))
            except AttributeError:
                pass

        return choices

    def _get_key_in_order(self):
        return []

    @property
    def ALL(self):
        return [getattr(self, attr_name) for attr_name in self._get_const_attrs()]

    @property
    def CHOICES(self):
        return self._get_choices()

    def export_to_js(self):
        """Return a dict mapping constant attribute names to values

        This method supports exporting constants to Javascript (see util/constants.py)
        """
        return {
            attr_name: getattr(self, attr_name) for attr_name in self._get_const_attrs()
        }

    def get_label(self, value):
        """Return the label associated with the given constant, or None

        The label is created with the as_choice() helper when creating the
        NameSpace object
        """
        for attr_name in self._get_const_attrs():
            try:
                attr = getattr(self, attr_name)
                if attr == value:
                    return attr.label
            except AttributeError:
                pass
        return None
