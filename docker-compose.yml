services:
  django:
    build:
      context: .
      dockerfile: ./compose/django/Dockerfile
    volumes:
      - .:/app
    ports:
      - "0.0.0.0:8000:8000"
    depends_on:
      - db
    networks:
      - shiptbot

  celerybeat:
    build:
      context: .
    entrypoint: bash -c "chown -R django /app; celery -A shiptbot.taskapp beat -l INFO -S django_celery_beat.schedulers:DatabaseScheduler"
    depends_on:
      - db
      - redis
    user: django
    volumes:
      - .:/app
    networks:
      - shiptbot

  celeryworker:
    build:
      context: .
    entrypoint: bash -c "chown -R django /app; celery -A shiptbot.taskapp worker -l INFO -Q priority_queue,default"
    depends_on:
      - db
      - redis
    user: django
    volumes:
      - .:/app
    networks:
      - shiptbot

  db:
    hostname: db
    image: postgres
    ports:
      - "5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - db_backup:/backups
    networks:
      - shiptbot

  frontend:
    build:
      context: .
      dockerfile: ./compose/frontend/Dockerfile
    volumes:
      - ./frontend:/frontend
    ports:
      - "0.0.0.0:8001:8001"
    networks:
      - shiptbot

  redis:
    image: redis:7.4.1
    networks:
      - shiptbot

networks:
  shiptbot:

volumes:
  db_backup:
  db_data:
