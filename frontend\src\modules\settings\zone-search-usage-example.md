# Zone Search Component Usage

## Overview
The `ZoneSearch` component provides a search interface for filtering zones and stores by zone name or store name. It's built with Material-UI components and follows the existing design patterns in the application.

## Features
- **Real-time search**: Filters zones and stores as you type
- **Dual search criteria**: Search by zone name or store name
- **Visual indicators**: Shows zone matches vs store matches with chips
- **Search results summary**: Displays count of matching stores and zones
- **Expandable zones**: Click to expand/collapse zone details
- **Clear search**: Easy-to-use clear button
- **Responsive design**: Works on mobile and desktop
- **Consistent styling**: Matches existing zone list styling

## Basic Usage

```jsx
import React, { useEffect } from 'react'
import { connect } from 'react-redux'
import API from 'data/api'
import { ZoneSearch } from 'modules/settings/components'

const MyComponent = ({ listStores }) => {
  useEffect(() => {
    // Load stores data when component mounts
    listStores()
  }, [listStores])

  return (
    <div>
      <h2>Search Zones and Stores</h2>
      <ZoneSearch />
    </div>
  )
}

const mapDispatchToProps = dispatch => ({
  listStores: () => dispatch(API.stores.list())
})

export default connect(null, mapDispatchToProps)(MyComponent)
```

## Integration with Existing Settings

To replace the existing ZoneList with ZoneSearch in the bot settings:

```jsx
// In bot-settings-screen.jsx
import { ZoneSearch } from './components'

// Replace ZoneList with ZoneSearch in the Zone tab
<TabPanel value={tabValue} index={0}>
  <ZoneSearch />
</TabPanel>
```

## Props
The component automatically connects to Redux store and doesn't require any props:
- `storesByZone`: Automatically injected from Redux store via `mapStateToProps`

## Data Structure
The component expects zone data in this format:
```json
{
  "id": "store_GTDpAx9feFnWcZ4DcPxefa",
  "zone_id": "1710",
  "zone_name": "Beaverton",
  "store_name": "Office Depot - SW Cedar Hills Blvd",
  "enabled": false,
  "min_pay": null,
  "min_pay_per_mile": null,
  "min_distance_miles": null,
  "max_distance_miles": null
}
```

## Search Behavior
1. **Empty search**: Shows all zones and stores
2. **Zone name match**: Shows entire zone with all its stores, marked with "Zone match" chip
3. **Store name match**: Shows only zones containing matching stores
4. **Mixed results**: Can show both zone matches and store matches
5. **Case insensitive**: Search is not case sensitive
6. **Real-time**: Results update as you type

## Styling
The component uses the same styling as the original ZoneList:
- Green color scheme for enabled stores
- Card-based layout with shadows
- Responsive design
- Consistent spacing and typography

## Demo
A demo component is available at `frontend/src/modules/settings/zone-search-demo.jsx` that shows both the new ZoneSearch and original ZoneList components side by side for comparison.
