import { useEffect } from 'react'
import { useStytchSession } from '@stytch/react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate, Navigate } from 'react-router-dom'
import { URL_PATH_AUTH_LOGIN } from 'constants/index'
import API from 'data/api'
import { getAuthUser } from 'data/auth/selectors'
import UserLoadingProgress from 'ui/user-loading-progress.jsx'

const PrivateRoute = (props) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const currentUser = useSelector(state => getAuthUser(state))
  const session = useStytchSession()

  useEffect(() => {
    if (session && !currentUser) {
      dispatch(API.auth.retrieveUser())
        .catch(() => {
          navigate(URL_PATH_AUTH_LOGIN)
        })
    }
  }, [])

  if (!session) {
    return <Navigate to={URL_PATH_AUTH_LOGIN} replace />
  } else {
    if (!currentUser) {
      return <UserLoadingProgress />
    } else {
      return <>{props.children}</>
    }
  }
}
export default PrivateRoute
