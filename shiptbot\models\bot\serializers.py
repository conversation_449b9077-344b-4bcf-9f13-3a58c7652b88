from rest_framework import serializers

from .models import BotSetting, Day, Offer, Range, Store


class BotSettingSerializer(serializers.ModelSerializer):
    class Meta:
        model = BotSetting
        fields = (
            "id",
            "refresh_rate_seconds",
            "stop_after_accepted",
            "stop_after_missed",
            "notis_after_accept",
            "notis_afer_missed",
            "notis_via_email",
            "notis_via_telegram",
        )
        read_only_fields = ("id",)

    def create(self, validated_data):
        raise NotImplementedError("Cannot create")


class StoreSerializer(serializers.ModelSerializer):
    class Meta:
        model = Store
        fields = (
            "id",
            "zone_id",
            "zone_name",
            "store_name",
            "enabled",
            "min_pay",
            "min_pay_per_mile",
            "min_distance_miles",
            "max_distance_miles",
        )
        read_only_fields = (
            "id",
            "zone_id",
            "zone_name",
            "store_name",
        )

    zone_id = serializers.CharField(source="zone.zone_id", read_only=True)
    zone_name = serializers.Char<PERSON>ield(source="zone.zone_name", read_only=True)

    def create(self, validated_data):
        raise NotImplementedError("Cannot create")


class DaySerializer(serializers.ModelSerializer):
    class Meta:
        model = Day
        fields = (
            "id",
            "day",
        )
        read_only_fields = (
            "id",
            "day",
        )

    def create(self, validated_data):
        raise NotImplementedError("Cannot create")

    def update(self, instance, validated_data):
        raise NotImplementedError("Cannot update")


class RangeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Range
        fields = (
            "id",
            "start_time",
            "end_time",
            "day",
            "overwrite_store_min_pay",
            "min_pay",
            "overwrite_store_distance",
            "min_distance_miles",
            "max_distance_miles",
        )
        read_only_fields = ("id",)

    def create(self, validated_data):
        # Get the user from the viewset context
        user = self.context["request"].user
        validated_data["user"] = user
        return super().create(validated_data)


class OfferSerializer(serializers.ModelSerializer):
    class Meta:
        model = Offer
        fields = (
            "id",
            "status",
            "created_at",
            "est_shopper_pay",
            "zone_name",
            "est_shop_time",
            "est_drive_time",
            "promo_pay",
            "order_status",
            "timeslot_info",
            "store_name",
            "store_location_name",
            "skip_reason",
        )
        read_only_fields = fields

    def create(self, validated_data):
        raise NotImplementedError("Cannot create")

    def update(self, instance, validated_data):
        raise NotImplementedError("Cannot update")
