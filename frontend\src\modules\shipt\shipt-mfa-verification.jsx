import React from 'react'
import { Box, Button, Grid2 } from '@mui/material'
import { TextFieldElement, useForm } from 'react-hook-form-mui'

const ShiptMFAVerification = props => {
  const { mfaData, verifyMfa } = props

  const { control, handleSubmit, watch } = useForm({
    code: ''
  })

  const onSubmit = data => {
    verifyMfa({ ...data, channel_id: mfaData.channel_id })
  }

  return (
    <Box component='form' onSubmit={handleSubmit(onSubmit)}>
      <Grid2 container spacing={2} size={12}>
        <Grid2 item xs={6}>
          <TextFieldElement fullWidth label='Code' name='code' control={control} required />
          <Button variant='contained' fullWidth color='primary' type='submit' sx={{ padding: '.5rem 2.5rem' }}>
            Submit
          </Button>
        </Grid2>
      </Grid2>
    </Box>
  )
}

export default ShiptMFAVerification
