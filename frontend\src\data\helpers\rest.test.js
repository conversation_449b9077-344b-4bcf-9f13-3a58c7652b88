import { RestChildResource, parentPkPattern, RestResource } from 'data/helpers/rest-resource'

/**
 * Tests for RestResource
 */

describe('RestResource', () => {
  it('should have default verbs', () => {
    const resource = new RestResource('/bundles', 'BUNDLES')
    expect(resource.verbs).toEqual(new Set(RestResource.prototype.baseOptions.verbs))
  })
})

describe('RestChildResource', () => {
  it('cannot be instantiated if url parameter is missing parentPkPattern', () => {
    const resourceFunction = () => {
      new RestChildResource('/tests/', 'TESTS') // eslint-disable-line no-new
    }
    expect(resourceFunction).toThrow(/url parameter must contain/)
  })

  it('has a default parent setter', () => {
    const resource = new RestChildResource(`/tests/${parentPkPattern}/`, 'TESTS')
    expect(resource.parent(43).buildPath('/')).toEqual('/tests/43/')
  })

  it('can have a custom parent setter', () => {
    const resource = new RestChildResource(`/tests/${parentPkPattern}/`, 'TESTS', 'foobar')
    expect(resource.foobar(19).buildPath('/')).toEqual('/tests/19/')
  })

  it('calling buildPath fails if parentPk is not set', () => {
    const resourceFunction = () => {
      const resource = new RestChildResource(`/tests/${parentPkPattern}/`, 'TESTS')
      resource.buildPath('')
    }
    expect(resourceFunction).toThrow(/ParentPk is required/)
  })

  it('calling buildPath succeeds when parentPk is set', () => {
    const resource = new RestChildResource(`/tests/${parentPkPattern}/`, 'TESTS')
    resource.parentPk = 12
    expect(resource.buildPath('/')).toEqual('/tests/12/')
  })
})
