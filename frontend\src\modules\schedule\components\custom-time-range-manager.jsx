import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  IconButton,
  Alert
} from '@mui/material'
import Grid2 from '@mui/material/Grid2'
import { TimePicker } from '@mui/x-date-pickers/TimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import AddIcon from '@mui/icons-material/Add'
import DeleteIcon from '@mui/icons-material/Delete'
import EditIcon from '@mui/icons-material/Edit'
import SaveIcon from '@mui/icons-material/Save'
import CancelIcon from '@mui/icons-material/Cancel'
import dayjs from 'dayjs'

const CustomTimeRangeManager = ({
  ranges,
  selectedDay,
  onCreateRange,
  onUpdateRange,
  onDeleteRange
}) => {
  const [newRange, setNewRange] = useState({
    start_time: null,
    end_time: null
  })
  const [editingRang<PERSON>, setEditingRange] = useState(null)
  const [editRange, setEditRange] = useState({
    start_time: null,
    end_time: null
  })
  const [errors, setErrors] = useState({})
  const [isAdding, setIsAdding] = useState(false)

  const validateTimeRange = (startTime, endTime) => {
    const errors = {}

    if (!startTime) {
      errors.start_time = 'Start time is required'
    }

    if (!endTime) {
      errors.end_time = 'End time is required'
    }

    if (startTime && endTime) {
      if (startTime.isAfter(endTime) || startTime.isSame(endTime)) {
        errors.end_time = 'End time must be after start time'
      }

      // Check for overlapping ranges
      const overlapping = ranges.find(range => {
        if (editingRange && range.id === editingRange.id) return false

        const rangeStart = dayjs(range.start_time, 'HH:mm:ss')
        const rangeEnd = dayjs(range.end_time, 'HH:mm:ss')

        return (
          (startTime.isBefore(rangeEnd) && endTime.isAfter(rangeStart)) ||
          (startTime.isSame(rangeStart) && endTime.isSame(rangeEnd))
        )
      })

      if (overlapping) {
        errors.overlap = 'Time range overlaps with existing range'
      }
    }

    return errors
  }

  const handleAddRange = async () => {
    const validationErrors = validateTimeRange(newRange.start_time, newRange.end_time)

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    try {
      // Create range with day information in a single API call
      await onCreateRange({
        start_time: newRange.start_time.format('HH:mm:ss'),
        end_time: newRange.end_time.format('HH:mm:ss'),
        day: selectedDay ? selectedDay.id : null
      })

      setNewRange({ start_time: null, end_time: null })
      setErrors({})
      setIsAdding(false)
    } catch (error) {
      console.error('Error creating range:', error)
      setErrors({ submit: 'Failed to create time range' })
    }
  }

  const handleEditRange = (range) => {
    setEditingRange(range)
    setEditRange({
      start_time: dayjs(range.start_time, 'HH:mm:ss'),
      end_time: dayjs(range.end_time, 'HH:mm:ss')
    })
    setErrors({})
  }

  const handleUpdateRange = async () => {
    const validationErrors = validateTimeRange(editRange.start_time, editRange.end_time)

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      return
    }

    try {
      await onUpdateRange(editingRange.id, {
        start_time: editRange.start_time.format('HH:mm:ss'),
        end_time: editRange.end_time.format('HH:mm:ss'),
        day: selectedDay ? selectedDay.id : null
      })

      setEditingRange(null)
      setEditRange({ start_time: null, end_time: null })
      setErrors({})
    } catch (error) {
      console.error('Error updating range:', error)
      setErrors({ submit: 'Failed to update time range' })
    }
  }

  const handleDeleteRange = async (rangeId) => {
    try {
      await onDeleteRange(rangeId)
    } catch (error) {
      console.error('Error deleting range:', error)
      setErrors({ submit: 'Failed to delete time range' })
    }
  }

  const handleCancelEdit = () => {
    setEditingRange(null)
    setEditRange({ start_time: null, end_time: null })
    setErrors({})
  }

  const handleCancelAdd = () => {
    setIsAdding(false)
    setNewRange({ start_time: null, end_time: null })
    setErrors({})
  }

  const formatTime = (timeString) => {
    const time = dayjs(timeString, 'HH:mm:ss')
    return time.format('h:mm A')
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant='h4' component='h2' sx={{ fontWeight: 600 }}>
            Bot Schedule
          </Typography>
          {!isAdding && (
            <Button
              variant='contained'
              startIcon={<AddIcon />}
              onClick={() => setIsAdding(true)}
              sx={{
                backgroundColor: '#2e7d32',
                '&:hover': {
                  backgroundColor: '#1b5e20'
                }
              }}
            >
              <Box component='span' sx={{ display: { xs: 'none', sm: 'inline' } }}>
                New schedule
              </Box>
              <Box component='span' sx={{ display: { xs: 'inline', sm: 'none' } }}>
                New
              </Box>
            </Button>
          )}
        </Box>

        {errors.submit && (
          <Alert severity='error' sx={{ mb: 2 }}>
            {errors.submit}
          </Alert>
        )}

        {/* Add new range form */}
        {isAdding && (
          <Card
            sx={{
              mb: 2,
              backgroundColor: 'white',
              borderRadius: 2,
              border: '2px solid #2e7d32',
              boxShadow: '0 2px 8px rgba(46, 125, 50, 0.15)'
            }}
          >
            <CardContent sx={{ py: 2, px: 2.5 }}>
              {/* Display overlap error at the top of the form */}
              {errors.overlap && (
                <Alert severity='error' sx={{ mb: 2, fontSize: '0.875rem' }}>
                  {errors.overlap}
                </Alert>
              )}

              <Grid2 container spacing={{ xs: 1.5, sm: 2 }} alignItems='flex-start'>
                <Grid2 size={{ xs: 6, sm: 5, md: 4 }}>
                  <Typography variant='body2' sx={{ mb: 1, fontWeight: 500, fontSize: '0.75rem' }}>
                    Start Time
                  </Typography>
                  <TimePicker
                    value={newRange.start_time}
                    onChange={(value) => {
                      setNewRange(prev => {
                        const newState = { ...prev, start_time: value }
                        // Reset end_time if it's now invalid (before or equal to new start_time)
                        if (prev.end_time && value && (prev.end_time.isBefore(value) || prev.end_time.isSame(value))) {
                          newState.end_time = null
                        }
                        return newState
                      })
                      setErrors(prev => ({ ...prev, start_time: null, overlap: null }))
                    }}
                    slotProps={{
                      textField: {
                        size: 'small',
                        error: !!errors.start_time,
                        helperText: errors.start_time,
                        fullWidth: true,
                        FormHelperTextProps: {
                          sx: {
                            fontSize: '0.75rem',
                            mt: 0.5,
                            mx: 0,
                            lineHeight: 1.2
                          }
                        }
                      }
                    }}
                  />
                </Grid2>

                <Grid2 size={{ xs: 6, sm: 5, md: 4 }}>
                  <Typography variant='body2' sx={{ mb: 1, fontWeight: 500, fontSize: '0.75rem' }}>
                    End Time
                  </Typography>
                  <TimePicker
                    value={newRange.end_time}
                    onChange={(value) => {
                      setNewRange(prev => ({ ...prev, end_time: value }))
                      setErrors(prev => ({ ...prev, end_time: null, overlap: null }))
                    }}
                    shouldDisableTime={(timeValue, clockType) => {
                      if (clockType === 'hours' && newRange.start_time) {
                        // Disable hours that are before or equal to start time hour
                        return timeValue <= newRange.start_time.hour()
                      }
                      return false
                    }}
                    slotProps={{
                      textField: {
                        size: 'small',
                        error: !!errors.end_time,
                        helperText: errors.end_time,
                        fullWidth: true,
                        FormHelperTextProps: {
                          sx: {
                            fontSize: '0.75rem',
                            mt: 0.5,
                            mx: 0,
                            lineHeight: 1.2
                          }
                        }
                      }
                    }}
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, sm: 2, md: 4 }}>
                  <Box sx={{
                    display: 'flex',
                    gap: 1,
                    justifyContent: { xs: 'center', sm: 'flex-start' },
                    mt: { xs: 2, sm: 3.5 }, // Adjusted to align with input fields
                    minHeight: '40px', // Ensure consistent height
                    alignItems: 'flex-start'
                  }}
                  >
                    <IconButton
                      onClick={handleAddRange}
                      size='small'
                      sx={{
                        color: '#2e7d32',
                        backgroundColor: '#e8f5e9',
                        minWidth: '40px',
                        minHeight: '40px',
                        '&:hover': {
                          backgroundColor: '#c8e6c9'
                        }
                      }}
                    >
                      <SaveIcon fontSize='small' />
                    </IconButton>
                    <IconButton
                      onClick={handleCancelAdd}
                      size='small'
                      sx={{
                        color: '#d32f2f',
                        backgroundColor: '#ffebee',
                        minWidth: '40px',
                        minHeight: '40px',
                        '&:hover': {
                          backgroundColor: '#ffcdd2'
                        }
                      }}
                    >
                      <CancelIcon fontSize='small' />
                    </IconButton>
                  </Box>
                </Grid2>
              </Grid2>
            </CardContent>
          </Card>
        )}

        {/* Existing ranges */}
        <Box>
          {ranges.length === 0 ? (
            <Card
              sx={{
                mb: 1.5,
                backgroundColor: 'white',
                border: '1px solid #e0e0e0',
                borderRadius: 2,
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
              }}
            >
              <CardContent sx={{ py: { xs: 3, sm: 4 }, px: { xs: 2, sm: 2.5 } }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography
                    variant='body1'
                    sx={{
                      color: '#666',
                      fontSize: { xs: '0.9rem', sm: '1rem' },
                      mb: 1
                    }}
                  >
                    No time ranges scheduled for this day
                  </Typography>
                  <Typography
                    variant='body2'
                    sx={{
                      color: '#999',
                      fontSize: { xs: '0.8rem', sm: '0.875rem' }
                    }}
                  >
                    Click "Add new schedule" to create your first time range
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          ) : (
            ranges.map((range) => (
              <Card
                key={range.id}
                sx={{
                  mb: 1.5,
                  backgroundColor: 'white',
                  border: '1px solid #e0e0e0',
                  borderRadius: 2,
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                }}
              >
                <CardContent sx={{ py: { xs: 1.5, sm: 2 }, px: { xs: 2, sm: 2.5 } }}>
                  {editingRange?.id === range.id ? (
                    <Box>
                      {/* Display overlap error at the top of the edit form */}
                      {errors.overlap && (
                        <Alert severity='error' sx={{ mb: 2, fontSize: '0.875rem' }}>
                          {errors.overlap}
                        </Alert>
                      )}

                      <Grid2 container spacing={{ xs: 1.5, sm: 2 }} alignItems='flex-start'>
                        <Grid2 size={{ xs: 6, sm: 5, md: 4 }}>
                          <Typography variant='body2' sx={{ mb: 1, fontWeight: 500, fontSize: '0.75rem' }}>
                            Start Time
                          </Typography>
                          <TimePicker
                            value={editRange.start_time}
                            onChange={(value) => {
                              setEditRange(prev => {
                                const newState = { ...prev, start_time: value }
                                // Reset end_time if it's now invalid (before or equal to new start_time)
                                if (prev.end_time && value && (prev.end_time.isBefore(value) || prev.end_time.isSame(value))) {
                                  newState.end_time = null
                                }
                                return newState
                              })
                              setErrors(prev => ({ ...prev, start_time: null, overlap: null }))
                            }}
                            slotProps={{
                              textField: {
                                size: 'small',
                                error: !!errors.start_time,
                                helperText: errors.start_time,
                                fullWidth: true,
                                FormHelperTextProps: {
                                  sx: {
                                    fontSize: '0.75rem',
                                    mt: 0.5,
                                    mx: 0,
                                    lineHeight: 1.2
                                  }
                                }
                              }
                            }}
                          />
                        </Grid2>

                        <Grid2 size={{ xs: 6, sm: 5, md: 4 }}>
                          <Typography variant='body2' sx={{ mb: 1, fontWeight: 500, fontSize: '0.75rem' }}>
                            End Time
                          </Typography>
                          <TimePicker
                            value={editRange.end_time}
                            onChange={(value) => {
                              setEditRange(prev => ({ ...prev, end_time: value }))
                              setErrors(prev => ({ ...prev, end_time: null, overlap: null }))
                            }}
                            shouldDisableTime={(timeValue, clockType) => {
                              if (clockType === 'hours' && editRange.start_time) {
                              // Disable hours that are before or equal to start time hour
                                return timeValue <= editRange.start_time.hour()
                              }
                              return false
                            }}
                            slotProps={{
                              textField: {
                                size: 'small',
                                error: !!errors.end_time,
                                helperText: errors.end_time,
                                fullWidth: true,
                                FormHelperTextProps: {
                                  sx: {
                                    fontSize: '0.75rem',
                                    mt: 0.5,
                                    mx: 0,
                                    lineHeight: 1.2
                                  }
                                }
                              }
                            }}
                          />
                        </Grid2>

                        <Grid2 size={{ xs: 12, sm: 2, md: 4 }}>
                          <Box sx={{
                            display: 'flex',
                            gap: 1,
                            justifyContent: { xs: 'center', sm: 'flex-start' },
                            mt: { xs: 2, sm: 3.5 }, // Adjusted to align with input fields
                            minHeight: '40px', // Ensure consistent height
                            alignItems: 'flex-start'
                          }}
                          >
                            <IconButton
                              onClick={handleUpdateRange}
                              size='small'
                              sx={{
                                color: '#2e7d32',
                                backgroundColor: '#e8f5e9',
                                minWidth: '40px',
                                minHeight: '40px',
                                '&:hover': {
                                  backgroundColor: '#c8e6c9'
                                }
                              }}
                            >
                              <SaveIcon fontSize='small' />
                            </IconButton>
                            <IconButton
                              onClick={handleCancelEdit}
                              size='small'
                              sx={{
                                color: '#d32f2f',
                                backgroundColor: '#ffebee',
                                minWidth: '40px',
                                minHeight: '40px',
                                '&:hover': {
                                  backgroundColor: '#ffcdd2'
                                }
                              }}
                            >
                              <CancelIcon fontSize='small' />
                            </IconButton>
                          </Box>
                        </Grid2>
                      </Grid2>
                    </Box>
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                    >
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant='h6'
                          component='div'
                          sx={{
                            fontWeight: 600,
                            fontSize: { xs: '1rem', sm: '1.1rem' }
                          }}
                        >
                          {formatTime(range.start_time)} - {formatTime(range.end_time)}
                        </Typography>
                      </Box>
                      <Box sx={{
                        display: 'flex',
                        gap: 1
                      }}
                      >
                        <IconButton
                          onClick={() => handleEditRange(range)}
                          size='small'
                          sx={{
                            color: '#2e7d32',
                            backgroundColor: '#e8f5e9',
                            minWidth: '40px',
                            minHeight: '40px',
                            '&:hover': {
                              backgroundColor: '#c8e6c9'
                            }
                          }}
                        >
                          <EditIcon fontSize='small' />
                        </IconButton>
                        <IconButton
                          onClick={() => handleDeleteRange(range.id)}
                          size='small'
                          sx={{
                            color: '#d32f2f',
                            backgroundColor: '#ffebee',
                            minWidth: '40px',
                            minHeight: '40px',
                            '&:hover': {
                              backgroundColor: '#ffcdd2'
                            }
                          }}
                        >
                          <DeleteIcon fontSize='small' />
                        </IconButton>
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </Box>
      </Box>
    </LocalizationProvider>
  )
}

export default CustomTimeRangeManager
