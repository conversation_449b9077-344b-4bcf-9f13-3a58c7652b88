import React from 'react'
import {
  Tabs,
  Tab,
  Box,
  Typography,
  Badge
} from '@mui/material'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import CancelIcon from '@mui/icons-material/Cancel'
import DoDisturbAltIcon from '@mui/icons-material/DoDisturbAlt'
import { OFFER_STATUS } from 'constants/backend'

const OffersTabs = ({
  activeTab,
  onTabChange,
  acceptedCount = 0,
  missedCount = 0
}) => {
  const handleTabChange = (event, newValue) => {
    onTabChange(newValue)
  }

  // Define colors for each tab
  const getTabColors = (tabValue) => {
    switch (tabValue) {
      case OFFER_STATUS.ACCEPTED:
        return {
          indicator: '#2e7d32', // Green
          selected: '#2e7d32',
          hover: '#2e7d32'
        }
      case OFFER_STATUS.GONE:
        return {
          indicator: '#d32f2f', // Red
          selected: '#d32f2f',
          hover: '#d32f2f'
        }
      case OFFER_STATUS.SKIPPED:
        return {
          indicator: '#f57c00', // Orange/Yellow
          selected: '#f57c00',
          hover: '#f57c00'
        }
      default:
        return {
          indicator: '#2e7d32',
          selected: '#2e7d32',
          hover: '#2e7d32'
        }
    }
  }

  const currentColors = getTabColors(activeTab)

  return (
    <Box sx={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        variant='fullWidth'
        sx={{
          backgroundColor: 'white',
          borderTopLeftRadius: { xs: 8, sm: 12 },
          borderTopRightRadius: { xs: 8, sm: 12 },
          '& .MuiTabs-indicator': {
            backgroundColor: currentColors.indicator,
            height: { xs: 2, sm: 3 }
          },
          '& .MuiTab-root': {
            minHeight: { xs: 56, sm: 64 },
            fontSize: { xs: '0.9rem', sm: '1rem' },
            fontWeight: 500,
            textTransform: 'none',
            color: '#666',
            padding: { xs: '8px 12px', sm: '12px 16px' },
            '&.Mui-selected': {
              fontWeight: 600
            },
            // Accepted tab colors
            '&[data-tab="accepted"]': {
              '&.Mui-selected': {
                color: '#2e7d32'
              },
              '&:hover': {
                color: '#2e7d32',
                opacity: 0.8
              }
            },
            // Gone tab colors
            '&[data-tab="gone"]': {
              '&.Mui-selected': {
                color: '#d32f2f'
              },
              '&:hover': {
                color: '#d32f2f',
                opacity: 0.8
              }
            },
            // Skipped tab colors
            '&[data-tab="skipped"]': {
              '&.Mui-selected': {
                color: '#f57c00'
              },
              '&:hover': {
                color: '#f57c00',
                opacity: 0.8
              }
            }
          },
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        <Tab
          value={OFFER_STATUS.ACCEPTED}
          data-tab='accepted'
          label={
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: { xs: 0.5, sm: 1 },
              flexWrap: { xs: 'wrap', sm: 'nowrap' },
              justifyContent: 'center'
            }}
            >
              <CheckCircleIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
              <Typography
                variant='inherit'
                sx={{ fontSize: { xs: '0.8rem', sm: '0.9rem' } }}
              >
                Accepted
              </Typography>

            </Box>
          }
        />
        <Tab
          value={OFFER_STATUS.GONE}
          data-tab='gone'
          label={
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: { xs: 0.5, sm: 1 },
              flexWrap: { xs: 'wrap', sm: 'nowrap' },
              justifyContent: 'center'
            }}
            >
              <CancelIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
              <Typography
                variant='inherit'
                sx={{ fontSize: { xs: '0.8rem', sm: '0.9rem' } }}
              >
                Gone
              </Typography>
            </Box>
          }
        />
        <Tab
          value={OFFER_STATUS.SKIPPED}
          data-tab='skipped'
          label={
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: { xs: 0.5, sm: 1 },
              flexWrap: { xs: 'wrap', sm: 'nowrap' },
              justifyContent: 'center'
            }}
            >
              <DoDisturbAltIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
              <Typography
                variant='inherit'
                sx={{ fontSize: { xs: '0.8rem', sm: '0.9rem' } }}
              >
                Skipped
              </Typography>

            </Box>
          }
        />
      </Tabs>
    </Box>
  )
}

export default OffersTabs
