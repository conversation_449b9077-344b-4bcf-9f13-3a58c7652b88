import { css } from '@emotion/react'
import styled from '@emotion/styled'

const drawerWidth = 260

const Main = styled.main`
  ${({ theme, open }) => css`
    ${theme.typography.mainContent};
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    transition: margin ${open ? theme.transitions.duration.enteringScreen : theme.transitions.duration.leavingScreen}ms ${open ? theme.transitions.easing.easeOut : theme.transitions.easing.sharp};
    background-color: rgb(238, 242, 246) !important;
    min-height: calc(-72px + 100vh);
    flex-grow: 1;
    padding: 20px;
    margin-top: 72px;
    margin-right: 20px;
    border-radius: 12px 12px 0px 0px;
    @media (max-width: 895.95px) {
      margin-left: 20px !important;
      width: calc(100% - 40px);
      padding-bottom: 80px; /* Space for bottom navigation */
      margin-top: 64px; /* Smaller margin for mobile */
      min-height: calc(-64px + 100vh);
    }
    @media (min-width: 900px) {
      margin-left: ${open ? '0' : `-${drawerWidth - 20}px`} !important;
      width: calc(100% - ${drawerWidth}px);
    }

    @media (max-width: 600px) {
      margin-left: 20px;
      width: calc(100% - 40px);
      padding: 16px;
      padding-bottom: 80px; /* Space for bottom navigation */
      margin-top: 64px; /* Smaller margin for mobile */
      min-height: calc(-64px + 100vh);
    }

    @media (max-width: 400px) {
      margin-left: 10px;
      width: calc(100% - 20px);
      padding: 16px;
      padding-bottom: 80px; /* Space for bottom navigation */
      margin-right: 10px;
      margin-top: 64px; /* Smaller margin for mobile */
      min-height: calc(-64px + 100vh);
    }
  `}
`

export default Main
