import secrets
import zoneinfo

import arrow
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _

from shiptbot.models.bot.constants import DAYS
from shiptbot.models.bot.models import BotSetting, Day
from shiptbot.models.subscription.models import Plan, Subscription
from shiptbot.services.stripe.helpers import Stripe<PERSON><PERSON>per
from shiptbot.util.buids import BUIDCode
from shiptbot.util.models import BaseModel

TIMEZONE_CHOICES = [(tz, tz) for tz in sorted(zoneinfo.available_timezones())]


class User(AbstractUser, BaseModel):
    BUID_OBJ_CODE = BUIDCode.USER

    email = models.EmailField(_("email address"), unique=True)
    stytch_user_id = models.CharField(
        max_length=100, unique=True, blank=True, null=True
    )
    password = models.CharField(max_length=128, blank=True, null=True)
    needs_initialization = models.BooleanField(default=True)
    stripe_customer_id = models.CharField(
        max_length=50, unique=True, blank=True, null=True
    )

    shipt_account_id = models.IntegerField(unique=True, blank=True, null=True)
    shipt_access_token = models.TextField(blank=True)
    shipt_refresh_token = models.TextField(blank=True)
    shipt_temp_token_data = models.JSONField(default=dict, blank=True, null=False)
    shipt_client_id = models.UUIDField(blank=True, null=True)
    shipt_is_running = models.BooleanField(default=False)

    timezone = models.CharField(max_length=64, choices=TIMEZONE_CHOICES, blank=True)
    latitude = models.FloatField(blank=True, null=True)
    longitude = models.FloatField(blank=True, null=True)

    def __str__(self):
        return self.full_name if self.first_name else self.email

    @staticmethod
    def get_random_username():
        return secrets.token_hex(6)

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def shipt_logged_in(self):
        return bool(self.shipt_access_token and self.shipt_refresh_token)

    def initialize_user(self):
        if not self.stripe_customer_id:
            StripeHelper.create_customer(self)

        for day in DAYS.ALL:
            Day.objects.get_or_create(user=self, day=day)

        BotSetting.objects.get_or_create(user=self)

        if not Subscription.objects.filter(user=self).exists():
            Subscription.objects.create(
                user=self,
                start_date=arrow.utcnow().datetime,
                end_date=arrow.utcnow().shift(days=settings.TRIAL_DAYS).datetime,
            )

    def get_latest_search_session(self):
        return self.session_set.first()
