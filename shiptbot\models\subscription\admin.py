from django.contrib import admin

from .models import Plan, Subscription


@admin.register(Plan)
class PlanAdmin(admin.ModelAdmin):
    search_fields = ("name",)
    readonly_fields = ("id",)
    list_display = ("name",)


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    search_fields = (
        "user__email",
        "plan__name",
    )
    readonly_fields = ("id",)
    raw_id_fields = ("user",)
    list_display = (
        "user",
        "plan",
        "start_date",
        "end_date",
    )
