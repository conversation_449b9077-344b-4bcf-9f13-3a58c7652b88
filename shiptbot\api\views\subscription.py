from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from shiptbot.models.subscription.models import Plan, Subscription
from shiptbot.models.subscription.serializers import (
    PlanSerializer,
    SubscriptionSerializer,
)
from shiptbot.services.stripe.helpers import StripeHelper
from shiptbot.util.api_exceptions import APIBadRequestError


class PlanViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = PlanSerializer

    def get_queryset(self):
        return Plan.objects.all()


class SubscriptionViewSet(viewsets.ModelViewSet):
    serializer_class = SubscriptionSerializer

    def get_queryset(self):
        return Subscription.objects.none()

    def get_object(self):
        return self.request.user.subscription

    @action(detail=False, methods=["get"])
    def self(self, request):
        return Response(SubscriptionSerializer(self.get_object()).data)

    @action(detail=False, methods=["post"])
    def checkout(self, request):
        plan_id = self.request.data.get("plan_id")
        if not plan_id:
            raise APIBadRequestError("Plan ID is required")

        try:
            plan = Plan.objects.get(id=plan_id)
        except Plan.DoesNotExist:
            raise APIBadRequestError("Plan does not exist")

        stripe_helper = StripeHelper(self.request.user)
        checkout_session = stripe_helper.create_checkout_session(plan)
        return Response({"url": checkout_session.url})

    @action(detail=False, methods=["get"])
    def billing_portal(self, request):
        stripe_helper = StripeHelper(self.request.user)
        billing_portal_session = stripe_helper.create_billing_portal_session()
        return Response({"url": billing_portal_session.url})
