import { URL_PATH_AUTH_AUTHENTICATE, URL_PATH_AUTH_LOGIN, URL_PATH_AUTH_LOGOUT } from 'constants/index'
import AuthLayout from 'layouts/auth-layout'
import AuthLogin from 'modules/auth/auth-login'
import AuthLogout from 'modules/auth/auth-logout'
import AuthAuthenticate from 'modules/auth/auth-authenticate'

const AUTH_PATH = '/auth'

const AUTH_ROUTES = {
  path: AUTH_PATH,
  element: <AuthLayout />,
  children: [
    { path: URL_PATH_AUTH_LOGIN, element: <AuthLogin /> },
    { path: URL_PATH_AUTH_LOGOUT, element: <AuthLogout /> },
    { path: URL_PATH_AUTH_AUTHENTICATE, element: <AuthAuthenticate /> }
  ]
}

export default AUTH_ROUTES
