import update from 'immutability-helper'
import invariant from 'invariant'
import getOr from 'lodash/fp/getOr'
import has from 'lodash/fp/has'
import isEmpty from 'lodash/fp/isEmpty'
import keys from 'lodash/fp/keys'
import mapValues from 'lodash/fp/mapValues'
import merge from 'lodash/fp/merge'
import mergeWith from 'lodash/fp/mergeWith'
import omit from 'lodash/fp/omit'
import pick from 'lodash/fp/pick'
import { handleActions } from 'redux-actions'

import { flattenById } from 'data/helpers/reducer'

import * as actions from './actions'
import { ENTITY_TYPES } from './constants'

const initialState = {
  [ENTITY_TYPES.STORES]: {},
  [ENTITY_TYPES.DAYS]: {},
  [ENTITY_TYPES.RANGES]: {},
  [ENTITY_TYPES.DAY_RANGES]: {},
  [ENTITY_TYPES.OFFERS]: {},
  [ENTITY_TYPES.BOTSETTINGS]: {},
  [ENTITY_TYPES.PLANS]: {}
}

// silently ignore broken actions unless we're in dev
function checkEntityType (entityType, state) {
  if (!has(entityType)(state)) {
    if (import.meta.env.NODE_ENV !== 'production') {
      invariant(false, `Entity type does not exist: ${entityType}.`)
    }
    return false
  }

  return true
}

/**
 * Merge entities into state, but picking the properties that are merged.
 *
 * const entityState = {
 *     users: {
 *         123: {
 *             given_name: 'Tom',
 *             family_name: 'Jones',
 *         },
 *         234: {
 *             given_name: 'Bob',
 *             family_name: 'Moses',
 *         },
 *     }
 * }
 * const newUsers = partialMergeEntities(entityState.users, 'given_name', {
 *     234: {
 *         given_name: 'Frank',     // will be merged
 *         family_name: 'Abagnale', // will not be merged
 *     }
 * });
 *
 * const newEntityState = set('users')(entityState);
 *
 * newEntityState.users.234.given_name === 'Frank'; // true
 * newEntityState.users.234.family_name === 'Moses'; // true
 *
 * @param oldEntities The portion of entity state that will be merged into
 * @param props an array of property keys that will be picked for each entity
 * @param newEntities The entities to merge into oldEntities
 * @param replaceProps Array of property keys that where the value in newEntities will replace the value in oldEntities
 *  (instead of deep merging)
 * @returns the merged entities
*/
export const partialMergeEntities = (oldEntities, props, newEntities, replaceProps = []) => {
  // Filter newEntities so that any keys not in oldEntities are removed
  const cleanNewEntities = pick(keys(oldEntities))(newEntities)

  let merger
  if (!isEmpty(replaceProps)) {
    // If we have replaceProps, then use a 'customizer' function that does not deep merge properties
    // with keys in replaceProps
    merger = mergeWith((objValue, srcValue, key) => replaceProps.includes(key) ? srcValue : undefined)
  } else {
    // otherwise, the regular deep merge behavior is fine
    merger = merge
  }

  return merger(oldEntities)(
    // this creates a new obj where each entity contains only the picked props
    mapValues(pick(props))(cleanNewEntities)
  )
}

export function rollbackifyId (id) {
  // Append string, so entities sort in a way that rollback is next to original
  return `${id}__`
}

/**
 *
 * @param state
 * @param payload an object with props that are one of the known ENTITY_TYPES, and values
 *  that are a single entity object, or a list of entity objects of that type. Like:
 *      {
 *          accounts: { ... a single account ... },
 *          users: [ { ... user 1 ...}, { ... user 2 ...}]
 *      }
 * @returns {*}
 */
function updateEntities (state, payload) {
  let newState = state

  // loop through payload, which will have entities
  Object.keys(payload).forEach((entityType) => {
    if (!checkEntityType(entityType, state)) {
      return
    }

    const entities = flattenById(payload[entityType])

    // Note we don't want to recursively merge (e.g. with lodash/fp/merge) because we want
    // nested array values (e.g. for related objects) to be replaced, not merged. update()
    // works this way.
    newState = update(newState, { [entityType]: { $merge: entities } })
  })

  return newState
}

const entitiesReducer = handleActions({
  [actions.update]: (state, action) => updateEntities(state, action.payload),
  [actions.remove] (state, action) {
    const { entity, id } = action.payload
    return update(state, { [entity]: { $unset: [id] } })
  },
  [actions.removeAll] (state, action) {
    let newState = state
    const { toDelete } = action.payload

    // toDelete is in obj with entityTypes as keys and list of pks as values
    Object.keys(toDelete).forEach((entityType) => {
      const idsToDelete = toDelete[entityType]

      if (!checkEntityType(entityType, state)) {
        return
      }

      newState = { ...newState, [entityType]: omit(idsToDelete)(state[entityType]) }
    })

    return newState
  },
  [actions.optimisticUpdate] (state, action) {
    const { entityType, entityId, data, partial = false } = action.payload
    const currentEntity = getOr(false, `${entityType}.${entityId}`)(state)

    // Only do optimistic updates on existing entities.
    // We could support optimistic 'create', but there was no need for it
    // at the time of adding this, so I didn't bother with it (2019-06-03)
    if (!currentEntity) {
      return state
    }

    // Give the rollbackEntity an alternative, predictable id value.
    const rollbackEntity = {
      ...currentEntity,
      id: rollbackifyId(entityId)
    }

    const optimisticEntity = partial
      ? {
          ...currentEntity,
          ...data,
          _pending: true
        }
      : {
          ...data,
          _pending: true
        }

    const entityPayload = {
      [entityType]: [optimisticEntity, rollbackEntity]
    }

    // Hack for performance when dealing with DocFields and DocFlecks, which are non-normalized
    // close copies of eachother. FIXME - get rid of this if we clean up state
    const newState = state

    return updateEntities(newState, entityPayload)
  },
  [actions.clear]: (state, action) => {
    const { entityType } = action.payload
    // replace key with an empty entity object
    return update(state, { [entityType]: { $set: {} } })
  },
  [actions.optimisticRollback] (state, action) {
    const { entityType, entityId } = action.payload
    const rollbackId = rollbackifyId(entityId)
    const stashedEntity = getOr(false, `${entityType}.${rollbackId}`)(state)
    let newState = state

    if (stashedEntity) {
      // Replace the original pk
      const rollbackEntity = {
        ...stashedEntity,
        id: entityId
      }

      const entityPayload = {
        [entityType]: [rollbackEntity]
      }

      // Insert the original stashed entity back into the state tree
      newState = updateEntities(newState, entityPayload)
    }

    // Remove the rollbackPk. This no-ops if the rollbackPk does not exist.
    return update(newState, { [entityType]: { $unset: [rollbackId] } })
  },
  // Remove the rollback entity from the entity state
  [actions.optimisticClear] (state, action) {
    const { entityType, entityId } = action.payload
    return update(state, { [entityType]: { $unset: [rollbackifyId(entityId)] } })
  }
}, initialState)

export default entitiesReducer
