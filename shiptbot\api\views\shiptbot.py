import uuid
from urllib.parse import urlparse

import pydash
from django.conf import settings
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from rest_framework import status, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from shiptbot.auth.serializers import UserSerializer
from shiptbot.models.bot.models import BotSetting
from shiptbot.models.bot.serializers import (
    BotSettingSerializer,
    DaySerializer,
    OfferSerializer,
    RangeSerializer,
    StoreSerializer,
)
from shiptbot.models.user.models import User
from shiptbot.services.bot.shipt_auth import ShiptAuth
from shiptbot.services.bot.shipt_bot import ShiptBot
from shiptbot.services.bot.tasks import async_start_bot
from shiptbot.util.api_exceptions import APIBadRequestError


class ShiptAuthViewSet(viewsets.ViewSet):
    """A viewset for shiptbot"""

    @action(detail=False, methods=["post"])
    def send_mfa(self, request):
        user = request.user

        shipt_auth = ShiptAuth(user)

        username = request.data.get("username")
        password = request.data.get("password")

        if not username or not password:
            raise APIBadRequestError("Username and password are required")

        token_data = shipt_auth.create_oauth_token(username, password)
        user.shipt_temp_token_data = token_data
        user.save()

        access_token = pydash.get(token_data, "access_token")
        mfa_channels = shipt_auth.get_mfa_channels(access_token)
        channel_id = pydash.get(mfa_channels, "channels[0].id")

        shipt_auth.send_mfa_code(access_token, channel_id)

        return Response({"channel_id": channel_id})

    @action(detail=False, methods=["post"])
    def verify_mfa(self, request):
        user = request.user

        channel_id = request.data.get("channel_id")
        code = request.data.get("code")

        if not channel_id or not code:
            raise APIBadRequestError("Channel ID or code are required")

        shipt_auth = ShiptAuth(user)
        access_token = pydash.get(user.shipt_temp_token_data, "access_token")

        token_data = shipt_auth.verify_mfa_code(access_token, channel_id, code)
        user.shipt_access_token = pydash.get(token_data, "access_token")
        user.shipt_refresh_token = pydash.get(token_data, "refresh_token")
        user.shipt_temp_token_data = {}
        user.save()

        return Response(UserSerializer(user).data)


class BotSettingViewSet(viewsets.ModelViewSet):
    serializer_class = BotSettingSerializer

    def get_queryset(self):
        return BotSetting.objects.none()

    def get_object(self):
        return self.request.user.botsetting


class StoreViewSet(viewsets.ModelViewSet):
    serializer_class = StoreSerializer

    def get_queryset(self):
        return self.request.user.store_set.all()

    @action(detail=False, methods=["put"])
    def refresh(self, request):
        user = request.user
        shiptbot = ShiptBot(user, None)
        shiptbot.refresh_zones_and_stores()
        return Response(self.get_serializer(user.store_set.all(), many=True).data)


class DayViewSet(viewsets.ModelViewSet):
    serializer_class = DaySerializer

    def get_queryset(self):
        return self.request.user.day_set.all()


class RangeViewSet(viewsets.ModelViewSet):
    serializer_class = RangeSerializer

    def get_queryset(self):
        queryset = self.request.user.range_set.all()

        # Filter by day if provided
        day = self.request.query_params.get("day", None)
        if day:
            queryset = queryset.filter(day=day)

        return queryset.order_by("start_time")


class OfferViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = OfferSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["session", "status"]

    def get_queryset(self):
        return self.request.user.offer_set.all()


class ShiptBotViewSet(viewsets.ViewSet):
    @action(detail=False, methods=["put"])
    def start(self, request):
        user = request.user

        if not user.shipt_logged_in:
            raise APIBadRequestError("User is not connected to Shipt Account")

        if user.shipt_is_running:
            raise APIBadRequestError("Search is already running")

        async_start_bot.delay(user.id)

        return Response(UserSerializer(user).data)

    @action(detail=False, methods=["post"])
    def stop(self, request):
        user = request.user

        if not user.shipt_is_running:
            raise APIBadRequestError("Search is not running")

        user.shipt_is_running = False
        user.save()

        return Response(UserSerializer(user).data)


@api_view(["POST"])
@permission_classes([AllowAny])
def auth0_shipt(request):
    """
    API endpoint to save Shipt access and refresh tokens for a user.
    Does not require authentication but requires user_id.
    Only allows requests from the configured AUTH0_SHIPT_SECRET_KEY.

    Domain restriction can be disabled by setting DJANGO_AUTH0_SHIPT_SECRET_KEY to an empty string.
    """
    try:
        # Get data from request
        data = request.data.get("data", {})

        # Check if domain restriction is enabled
        secret_key = getattr(settings, "AUTH0_SHIPT_SECRET_KEY", "")

        if secret_key:
            request_secret_key = data.get("key")
            if not request_secret_key or secret_key != request_secret_key:
                return Response(
                    {
                        "error": "Request not allowed from this domain",
                        "message": "Authentication failed - invalid or missing secret key"
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

        # Extract required fields
        user_id = data.get("user_id")
        access_token = data.get("access_token")
        refresh_token = data.get("refresh_token")
        client_id = data.get("client_id")

        # Validate required fields
        if not user_id:
            return Response(
                {
                    "error": "user_id is required",
                    "message": "Missing required field: user_id"
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        if not access_token:
            return Response(
                {
                    "error": "access_token is required",
                    "message": "Missing required field: access_token"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not refresh_token:
            return Response(
                {
                    "error": "refresh_token is required",
                    "message": "Missing required field: refresh_token"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if user exists
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response(
                {
                    "error": "User not found",
                    "message": f"User not found"
                },
                status=status.HTTP_404_NOT_FOUND
            )

        # Save tokens to user
        user.shipt_access_token = access_token
        user.shipt_refresh_token = refresh_token

        try:
            account_info = ShiptBot(user).get_account_info()
            account_id = pydash.get(account_info, "id", None)

            if not account_id:
                return Response(
                    {
                        "error": "Failed to get account info",
                        "message": "Unable to retrieve account ID from Shipt API"
                    },
                    status=status.HTTP_406_NOT_ACCEPTABLE,
                )
        except Exception as e:
            return Response(
                {
                    "error": "Failed to get account info",
                    "message": f"Unable to retrieve account ID from Shipt API"
                },
                status=status.HTTP_406_NOT_ACCEPTABLE,
            )

        if User.objects.filter(shipt_account_id=account_id).exists():
            return Response(
                {
                    "error": "Shipt Account already exists",
                    "message": f"A user is already connected to Shipt account ID"
                },
                status=status.HTTP_409_CONFLICT,
            )

        # Save client_id if provided (convert to UUID if it's a string)
        if client_id:
            try:
                # Try to convert string to UUID if needed
                if isinstance(client_id, str):
                    import uuid as uuid_module

                    user.shipt_client_id = uuid_module.UUID(client_id)
                else:
                    user.shipt_client_id = client_id
            except ValueError:
                return Response(
                    {
                        "error": "Invalid client_id format. Must be a valid UUID.",
                        "message": f"The provided client_id '{client_id}' is not a valid UUID format"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # user.shipt_account_id = account_id
        user.save()

        return Response(
            {
                "message": "Tokens saved successfully",
                "user_id": str(user.id),
                "shipt_logged_in": user.shipt_logged_in,
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        return Response(
            {
                "error": f"Internal server error",
                "message": "An unexpected error occurred while processing the request"
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
