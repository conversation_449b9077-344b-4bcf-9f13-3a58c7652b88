import Cookie from 'js-cookie'
import getOr from 'lodash/fp/getOr'

import { ONE_DAY_MINUTES, AUTH_HEADER_PREFIX_STYTCH, STYTCH_SESSION_JWT_COOKIE_NAME } from './constants'
import { URL_PATH_AUTH_AUTHENTICATE } from 'constants/index'

const buildAbsoluteUrl = (path) => {
  return `${window.location.origin}${path}`
}

export const stytchHelper = {
  active: !!getOr('', 'VITE_REACT_APP_STYTCH_PUBLIC_TOKEN')(import.meta.env),
  publicToken: getOr('', 'VITE_REACT_APP_STYTCH_PUBLIC_TOKEN')(import.meta.env),
  loginSignupRedirectUrl: getOr(buildAbsoluteUrl(URL_PATH_AUTH_AUTHENTICATE), 'VITE_REACT_APP_STYTCH_LOGIN_SIGNUP_REDIRECT_URL')(import.meta.env),
  sessionDurationMinutes: parseInt(getOr(ONE_DAY_MINUTES, 'VITE_REACT_APP_STYTCH_SESSION_DURATION_MINUTES')(import.meta.env)),
  hasStytchSessionJwt: () => !!Cookie.get(STYTCH_SESSION_JWT_COOKIE_NAME),
  getStytchSessionJwt: () => `${AUTH_HEADER_PREFIX_STYTCH} ${Cookie.get(STYTCH_SESSION_JWT_COOKIE_NAME)}`
}
