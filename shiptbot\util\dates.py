"""This module contains date related constants and methods.

Note that (almost) all date format strings use the notation from the Python Arrow
library (https://pypi.org/project/arrow/). These format strings start with a prefix
of ARROW_FMT_. We do this to prevent any ambiguity when we see a date format constant
in code. There are a few exceptions and those are commented in this file.
"""

import datetime
import re
import zoneinfo
from typing import Union

import arrow
from django.utils import dateparse

MINUTES_PER_DAY = 24 * 60

# Date format being used in the front end
ARROW_FMT_DEFAULT_DATE_DISPLAY_STR = "M/D/YY"
ARROW_FMT_DEFAULT_DATETIME_DISPLAY_STR = "M/D/YY h:mm a"
ARROW_FMT_DEFAULT_MONTH_DISPLAY_STR = "YYYY-MM"
ARROW_FMT_DEFAULT_TIME_DISPLAY_STR = "h:mm a"

# String format for date storage in DocFleck.value.val_date.
# This is an ISO 8601 format that can be parsed consistently
# by moment.js across supported browsers. Note there is no
# timezone info, as we don't ever want to show a different
# date due to timezone - as this date represents user
# input.
ARROW_FMT_DATE_VALUE_STR = "YYYY-MM-DD"

# String format for datetime storage in DocFleck.value.val_datetime.
# This is an ISO 8601 format that can be parsed consistently
# by moment.js across supported browsers. Note 'Z' for UTC timezone.
#  All datetimes should be in UTC.
ARROW_FMT_DATETIME_VALUE_STR = "YYYY-MM-DDTHH:mm:ssZ"

# Date in iso 8601 format, but only allowing the full YYYY-MM-DD, not
# YYYY-MM or YYYY. 'Strict' is not a common iso 8601 term, just one
# we made up for this purpose
ARROW_FMT_DATE_ISO_8601_STRICT = "YYYY-MM-DD"

# Date + time in iso 8601 format
# It should be used in DateTimeField only
DATETIME_FORMAT_ISO_8601_STRICT = "%Y-%m-%dT%H:%M:%SZ"
# Note an arrow obj must be in utc for this to be correct
ARROW_FMT_DATETIME_ISO_8601 = "YYYY-MM-DDTHH:mm:ss[Z]"

# date suitable for use as part of a filename
ARROW_FMT_DATE_FILENAME = "YYYY-MM-DD"

# basic month/day/year
ARROW_FMT_DATE_MDY = "MM/DD/YYYY"

# month/day/year with 2 digit year, and leading zeros on month and day
ARROW_FMT_DATE_MDY_SHORT = "MM/DD/YY"
ARROW_FMT_DATETIME_MDY_TIMEZONE = "MM/DD/YY HH:mm"

# Close to one of the common / 'standard' Excel date formats,
# but with leading 0s for month / day.
# See: https://support.office.com/en-us/article/Format-a-date-the-way-you-want-8e10019e-d5d8-47a1-ba95-db95123d273e#__toc320186182
ARROW_FMT_DATE_EXCEL = "MM/DD/YY"

# Excel Date Formats - these are appropriate for dates that are output
# in CSV that might need to be opened and read by Microsoft Excel, or other Excel-like
# spreadsheets. These formats are close to one of the common / "standard" Excel
# date formats, but with leading 0s for month / day.
ARROW_FMT_DATETIME_EXCEL = "MM/DD/YYYY hh:mm:ss A"  # 12 hour version
ARROW_FMT_DATETIME_EXCEL_24 = "MM/DD/YYYY HH:mm:ss A"  # 24 hour version

# default formats for fields in frontend and PDF
ARROW_FMT_DATE_FIELD_DEFAULT = "M/D/YY"
ARROW_FMT_DATETIME_FIELD_DEFAULT = "M/D/YY HH:mm ZZZ"

ARROW_FMT_DATE_CHOICES = [
    ("", "Default: 1/31/20"),  # Corresponds to ARROW_FMT_DATE_FIELD_DEFAULT
    ("M/D/YY", "1/31/20"),
    ("MM/DD/YY", "01/31/20"),
    ("MM/DD/YYYY", "01/31/2020"),
    ("YYYY-MM-DD", "2020-01-31"),
    ("D/M/YY", "31/1/20"),
    ("D/M/YYYY", "31/1/2020"),
    ("DD/MM/YYYY", "31/01/2020"),
    ("MMM D, YYYY", "Jan 31, 2020"),
    ("MMMM D, YYYY", "January 31, 2020"),
    ("MMMM Do, YYYY", "January 31st, 2020"),
    ("D MMM YYYY", "31 Jan 2020"),
    ("D MMMM YYYY", "31 January 2020"),
]

ARROW_FMT_DASHBOARD_DATE_CHOICES = [
    ("", "Default: 1/31/20"),  # Corresponds to ARROW_FMT_DATE_FIELD_DEFAULT
    ("MM/DD/YY", "01/31/20"),
    ("YYYY-MM-DD", "2020-01-31"),
    ("D/M/YY", "31/1/20"),
    ("DD/MM/YYYY", "31/01/2020"),
    # ('MMM D, YYYY', 'Jan 31, 2020'),
    # ('D MMM YYYY', '31 Jan 2020'),
]

ARROW_FMT_SIGNING_DATETIME_FULL = "YYYY-MM-DD HH:mm:ss ZZZ"
ARROW_FMT_SIGNING_DATETIME_CHOICES = (
    [
        ("", "Default: 1/31/20, 1:15 pm EST"),
    ]
    + ARROW_FMT_DATE_CHOICES[1:]
    + [
        ("M/D/YY, h:mm a ZZZ", "1/31/20, 1:15 pm EST"),
        ("MM/DD/YYYY, HH:mm ZZZ", "01/31/2020, 13:15 EST"),
        ("D/M/YY, h:mm a ZZZ", "31/1/20, 1:15 pm EST"),
        ("DD/MM/YYYY, HH:mm ZZZ", "31/01/2020, 13:15 EST"),
        ("MMM D, YYYY, h:mm a ZZZ", "Jan 31, 2020, 1:15 pm EST"),
        ("D MMM YYYY, h:mm a ZZZ", "31 Jan 2020, 1:15 pm EST"),
        ("D MMM YYYY, HH:mm ZZZ", "31 Jan 2020, 13:15 EST"),
        (ARROW_FMT_SIGNING_DATETIME_FULL, "2020-01-31 13:15:22 EST"),
    ]
)

ARROW_FMT_SIGNING_DATETIME_DEFAULT = "MMM D, YYYY, h:mm a ZZZ"


# Exceptions to ARROW_FMT_ convention.
# These are used in Django Rest Framework serializer fields that expect python
# datetime-style format strings
DATE_FORMAT_ISO_8601_STRICT = "%Y-%m-%d"
DATETIME_FORMAT_ISO_8601_STRICT = "%Y-%m-%dT%H:%M:%SZ"


def get_normalized_date(date_str):
    """Return a date object corresponding to the date in date_str

    Note this does not do any timezone handling. The UTC date_str with time is
    returned as the corresponding date (essentially, truncating the time from the
    UTC datetime, leaving a timezone-free date)

    Args:
        date_str: A string representing a date in ISO 8601 format, with or without
            time information. String format is either like '2017-08-25' or like
            '2016-09-20T22:41:05.835Z'

    Returns:
        a datetime.date object or None if parsing / normalization fails
    """
    datetime_obj = dateparse.parse_datetime(date_str)
    if datetime_obj:
        date = datetime_obj.date()
    else:
        date = dateparse.parse_date(date_str)

    return date


def get_today_short(tz=None, format="YYYY-MM-DD"):
    now = arrow.utcnow()
    if tz:
        now = now.to(tz)

    return now.format(format)


def custom_timezone_sort(choice):
    """Put US and UTC at the top of the list"""
    tz_str, dontcare = choice
    if tz_str.startswith("UTC"):
        return "0" + tz_str

    if tz_str.startswith("US"):
        return "1" + tz_str

    return tz_str


def format_arrow_for_val_datetime(arw):
    return "{}".format(arw.to("utc").format(ARROW_FMT_DATETIME_ISO_8601))


def arw_format(
    dt: datetime.datetime, format_str: str, timezone: Union[str, datetime.tzinfo] = None
):
    """Format a datetime obj using an arrow format string, with an optional timezone

    Args:
        dt: the datetime.datetime object
        format_str: the format string, using Arrow's format notation
        timezone: an optional timezone

    Returns:
        The datetime formatted as a string
    """
    arw = arrow.get(dt)
    if timezone:
        arw = arw.to(timezone)
    return arw.format(format_str)


TIMEZONE_CHOICES = [(tz, tz) for tz in zoneinfo.available_timezones()]


class CustomArrow(arrow.Arrow):
    """Custom Arrow class with zzz support

    This is probably not necessary anymore because of Arrow's changed
    handling of ZZZ. But we will leave it around for now (2020-07)
    """

    RE_zzz = r"zzz"

    def format(self, fmt="YYYY-MM-DD HH:mm:ssZZ", locale="en_us"):
        """Add support for zzz format token, which behaves the same as %Z for datetime.strftime"""

        def repl_tz_short(matchobj):
            return "[{}]".format(self._datetime.strftime("%Z"))

        fmt = re.sub(self.RE_zzz, repl_tz_short, fmt)
        return super().format(fmt, locale)


arrow_factory = arrow.ArrowFactory(CustomArrow)
