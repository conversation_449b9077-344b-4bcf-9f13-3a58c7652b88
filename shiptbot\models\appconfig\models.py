import decimal
import json
from json import JSONDecodeError

from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils import dateparse
from django.utils.text import slugify

from shiptbot.util.models import ValidatingBaseModel


def _prep_field(kind, val):
    if kind == "JSN":
        if not isinstance(val, str):
            val = json.dumps(val)
    else:
        if not isinstance(val, str):
            val = str(val)

    return val


def _value_as_type(kind, value):
    if kind == "BLN":
        if str(value).lower() in ("true", "t", "1"):
            return True
        else:
            return False
    elif kind == "INT":
        return int(value)
    elif kind in ("DAT", "DTM"):
        return dateparse(value)
    elif kind == "DEC":
        return decimal.Decimal(value)
    elif kind == "FLT":
        return float(value)
    elif kind == "JSN":
        return json.loads(value)

    return value


class Setting(ValidatingBaseModel):
    class Meta:
        ordering = ["name"]
        unique_together = (
            "metadata",
            "slug",
        )

    DATE = "DAT"
    TIME = "TIM"
    DATETIME = "DTM"
    URL = "URL"
    MARKDOWN = "MKD"
    STRING = "STR"
    TEXT = "TXT"
    LARGE_TEXT = "LTX"
    JSON = "JSN"
    HTML = "HTM"
    BOOL = "BLN"
    INTEGER = "INT"
    DECIMAL = "DEC"
    FLOAT = "FLT"
    TYPE_CHOICES = (
        (DATE, "Date"),
        (TIME, "Time"),
        (DATETIME, "Datetime"),
        (URL, "URL"),
        (MARKDOWN, "Markdown"),
        (STRING, "String"),
        (TEXT, "Text"),
        (LARGE_TEXT, "Large Text"),
        (JSON, "JSON"),
        (HTML, "HTML"),
        (BOOL, "Boolean"),
        (INTEGER, "Integer"),
        (FLOAT, "Float"),
        (DECIMAL, "Decimal"),
    )

    kind = models.CharField(
        max_length=3,
        choices=TYPE_CHOICES,
        default=STRING,
    )
    name = models.CharField(max_length=200, unique=False)
    slug = models.SlugField(max_length=200, editable=True)
    default = models.TextField(default="", blank=True)
    metadata = JSONField(default=dict, blank=True, null=False)
    note = models.TextField(blank=True, help_text="Internal note describing setting")

    def __str__(self):
        return "Setting {} - {}".format(self.id, self.name)

    def __repr__(self):
        return "<Setting {} {}>".format(self.id, self.name[:10])

    @property
    def value(self):
        return _value_as_type(self.kind, self.default)

    def clean(self):
        if self.kind == self.JSON and self.default:
            try:
                json.loads(self.default)
            except JSONDecodeError as e:
                raise ValidationError(
                    {
                        "default": [
                            "This value is not valid JSON. {}".format(e),
                        ]
                    }
                )

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        self.default = _prep_field(self.kind, self.default)

        super().save(*args, **kwargs)
