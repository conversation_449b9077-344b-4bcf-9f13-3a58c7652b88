// Demo data for testing OfferCard component with new data structure
export const demoOffers = [
  {
    detail_view_url: 'aviator/v1/shopper/orders/6393102940/view_unclaimed',
    id: 386614851,
    obfuscated_id: 6393102940,
    customer: {
      id: 68473132,
      name: '<PERSON>',
      email: '<EMAIL>',
      default_address_id: '106415839',
      full_name: '<PERSON>'
    },
    tracked_properties: {
      bundle_id: '8416e765-592f-4383-af5d-7ea54da63c9f',
      order_type: 'offer',
      order_id: 386614851,
      order_status: 'open',
      timeslot_info: '2025-07-05T19:00:00Z',
      est_shop_start_time: '2025-07-05T19:00:00Z',
      est_shop_time: 5.334371968370806,
      est_drive_time: 22.046380468095585,
      store_id: 27,
      store_name: 'Target',
      store_location_id: '4270',
      store_location_name: 'Target - Portland NW',
      metro_id: 136,
      zone_name: 'Portland North',
      estimated_pay: '10.47',
      is_bundle: true,
      section_name: ''
    }
  },
  {
    detail_view_url: 'aviator/v1/shopper/orders/6393102941/view_unclaimed',
    id: 386614852,
    obfuscated_id: 6393102941,
    customer: {
      id: 68473133,
      name: 'Maria L.',
      email: '<EMAIL>',
      default_address_id: '106415840',
      full_name: 'Maria Lopez'
    },
    tracked_properties: {
      bundle_id: '8416e765-592f-4383-af5d-7ea54da63c9g',
      order_type: 'delivery',
      order_id: 386614852,
      order_status: 'accepted',
      timeslot_info: '2025-07-05T20:30:00Z',
      est_shop_start_time: '2025-07-05T20:30:00Z',
      est_shop_time: 8.5,
      est_drive_time: 15.2,
      store_id: 28,
      store_name: 'Walmart',
      store_location_id: '4271',
      store_location_name: 'Walmart Supercenter - Portland SE',
      metro_id: 136,
      zone_name: 'Portland South',
      estimated_pay: '15.25',
      is_bundle: false,
      section_name: ''
    }
  },
  {
    detail_view_url: 'aviator/v1/shopper/orders/6393102942/view_unclaimed',
    id: 386614853,
    obfuscated_id: 6393102942,
    customer: {
      id: 68473134,
      name: 'John D.',
      email: '<EMAIL>',
      default_address_id: '106415841',
      full_name: 'John Davis'
    },
    tracked_properties: {
      bundle_id: '8416e765-592f-4383-af5d-7ea54da63c9h',
      order_type: 'offer',
      order_id: 386614853,
      order_status: 'missed',
      timeslot_info: '2025-07-05T18:00:00Z',
      est_shop_start_time: '2025-07-05T18:00:00Z',
      est_shop_time: 12.75,
      est_drive_time: 28.5,
      store_id: 29,
      store_name: 'Kroger',
      store_location_id: '4272',
      store_location_name: 'Kroger - Portland Downtown',
      metro_id: 136,
      zone_name: 'Portland Central',
      estimated_pay: '18.90',
      is_bundle: true,
      section_name: ''
    }
  },
  {
    detail_view_url: 'aviator/v1/shopper/orders/6393102943/view_unclaimed',
    id: 386614854,
    obfuscated_id: 6393102943,
    customer: {
      id: 68473135,
      name: 'Sarah M.',
      email: '<EMAIL>',
      default_address_id: '106415842',
      full_name: 'Sarah Miller'
    },
    tracked_properties: {
      bundle_id: '8416e765-592f-4383-af5d-7ea54da63c9i',
      order_type: 'delivery',
      order_id: 386614854,
      order_status: 'completed',
      timeslot_info: '2025-07-05T16:30:00Z',
      est_shop_start_time: '2025-07-05T16:30:00Z',
      est_shop_time: 6.25,
      est_drive_time: 18.75,
      store_id: 30,
      store_name: 'Safeway',
      store_location_id: '4273',
      store_location_name: 'Safeway - Portland West',
      metro_id: 136,
      zone_name: 'Portland West',
      estimated_pay: '12.80',
      is_bundle: false,
      section_name: ''
    }
  }
]
