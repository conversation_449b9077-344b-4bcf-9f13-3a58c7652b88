from rest_framework import status

from shiptbot.util.api_exceptions import BaseErrorSchema


class AuthException:
    def __init__(self):
        pass

    def expired_signature(self):
        """
        Generates a BaseErrorSchema for an expired session signature.

        Returns:
            BaseErrorSchema: An error schema with the following attributes:
                - code: "AUTH_SESSION_EXPIRED"
                - status_code: HTTP 401 Unauthorized
                - message: "Authenticated Error: Session expired."
                - details: "Authenticated Error: Session expired."
        """
        return BaseErrorSchema(
            code="AUTH_SESSION_EXPIRED",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="Authenticated Error: Session expired.",
            details="Authenticated Error: Session expired.",
        )

    def bad_token(self):
        """
        Generates a `BaseErrorSchema` for an invalid token error.

        Returns:
            BaseErrorSchema: An error schema with code "AUTH_BAD_TOKEN",
                             status code 401 (Unauthorized),
                             message "Bad token.",
                             and details "The provided token cannot be decoded."
        """
        return BaseErrorSchema(
            code="AUTH_BAD_TOKEN",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="Bad token.",
            details="The provided token cannot be decoded.",
        )

    def invalid_token(self):
        """
        Generates an error response indicating that the provided token is invalid.

        Returns:
            BaseErrorSchema: An error schema object with the following attributes:
                - code: A string representing the error code ("AUTH_INVALID_TOKEN").
                - status_code: An HTTP status code indicating unauthorized access (401).
                - message: A brief message describing the error ("Invalid token.").
                - details: Additional details about the error ("The provided credentials are invalid").
        """
        return BaseErrorSchema(
            code="AUTH_INVALID_TOKEN",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="Invalid token.",
            details="The provided credentials are invalid",
        )

    def runtime_error(self):
        """
        Generates a runtime error response.

        Returns:
            BaseErrorSchema: An error schema object with the following attributes:
                - code: "AUTH_RUNTIME_ERROR"
                - status_code: HTTP 500 Internal Server Error
                - message: "Temporary failure. Please try again."
                - details: "Runtime error occurred while processing the authenticate."
        """
        return BaseErrorSchema(
            code="AUTH_RUNTIME_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message="Temporary failure. Please try again.",
            details="Runtime error occurred while processing the authenticate.",
        )

    def invalid_payload(self):
        """
        Generates an error schema for an invalid payload.

        Returns:
            BaseErrorSchema: An error schema object with the following attributes:
                - code: "AUTH_INVALID_PAYLOAD"
                - status_code: HTTP 401 Unauthorized
                - message: "Invalid payload."
                - details: "The payload is invalid."
        """
        return BaseErrorSchema(
            code="AUTH_INVALID_PAYLOAD",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="Invalid payload.",
            details="The payload is invalid.",
        )

    def no_valid_user(self):
        """
        Generates a BaseErrorSchema indicating that no valid user was found.

        Returns:
            BaseErrorSchema: An error schema with the following attributes:
                - code: "AUTH_NO_VALID_USER"
                - status_code: HTTP 401 Unauthorized
                - message: "No valid user."
                - details: "No valid user found."
        """
        return BaseErrorSchema(
            code="AUTH_NO_VALID_USER",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="No valid user.",
            details="No valid user found.",
        )

    def user_is_not_active(self):
        """
        Generates an error response indicating that the user is not active.

        Returns:
            BaseErrorSchema: An error schema with the following attributes:
                - code: "AUTH_USER_IS_NOT_ACTIVE"
                - status_code: HTTP 401 Unauthorized
                - message: "User is not active."
                - details: "User is not active."
        """
        return BaseErrorSchema(
            code="AUTH_USER_IS_NOT_ACTIVE",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="User is not active.",
            details="User is not active.",
        )

    def invalid_header(self):
        """
        Generates an error response for invalid authorization headers.

        Returns:
            BaseErrorSchema: An error schema object containing the error code,
                             HTTP status code, error message, and details.
        """
        return BaseErrorSchema(
            code="AUTH_INVALID_HEADER",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="Invalid header.",
            details="Invalid Authorization header. No credentials provided.",
        )

    def header_contain_spaces(self):
        """
        Generates an error schema indicating that the authorization header contains spaces.

        Returns:
            BaseErrorSchema: An error schema with the following attributes:
                - code: "AUTH_HEADER_CONTAIN_SPACES"
                - status_code: HTTP 401 Unauthorized
                - message: "Invalid header"
                - details: "Invalid Authorization header. Credentials string should not contain spaces."
        """
        return BaseErrorSchema(
            code="AUTH_HEADER_CONTAIN_SPACES",
            status_code=status.HTTP_401_UNAUTHORIZED,
            message="Invalid header",
            details="Invalid Authorization header. Credentials string should not contain spaces.",
        )
