# Use an official node image as the base image
FROM node:24-alpine

# Set the working directory inside the container
WORKDIR /app


# Copy the package.json and yarn.lock files
COPY ./frontend/package.json ./frontend/yarn.lock ./

# Install dependencies
RUN yarn install

# Copy the rest of the application code to the container
COPY ./frontend .

ARG VITE_REACT_APP_BACKEND_URL
ARG VITE_REACT_APP_STYTCH_LOGIN_SIGNUP_REDIRECT_URL
ARG VITE_REACT_APP_STYTCH_PUBLIC_TOKEN
ARG VITE_REACT_APP_STYTCH_SESSION_DURATION_MINUTES
ARG VITE_REACT_APP_AUTH0_LOGIN_URL

ENV VITE_REACT_APP_BACKEND_URL $VITE_REACT_APP_BACKEND_URL
ENV VITE_REACT_APP_STYTCH_LOGIN_SIGNUP_REDIRECT_URL $VITE_REACT_APP_STYTCH_LOGIN_SIGNUP_REDIRECT_URL
ENV VITE_REACT_APP_STYTCH_PUBLIC_TOKEN $VITE_REACT_APP_STYTCH_PUBLIC_TOKEN
ENV VITE_REACT_APP_STYTCH_SESSION_DURATION_MINUTES $VITE_REACT_APP_STYTCH_SESSION_DURATION_MINUTES
ENV VITE_REACT_APP_AUTH0_LOGIN_URL $VITE_REACT_APP_AUTH0_LOGIN_URL

ENV VITE_REACT_APP_USE_STRICT_MODE $VITE_REACT_APP_USE_STRICT_MODE

# COPY ./compose/frontend/frontend.sh /frontend.sh
# RUN chmod +x /frontend.sh

RUN yarn run build

WORKDIR /app/express
RUN yarn install

EXPOSE 8001
CMD ["yarn", "run", "start"]
