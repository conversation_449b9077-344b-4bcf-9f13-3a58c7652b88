import { selectors, ENTITY_TYPES } from 'data/entities/index'

/**
 * Get all days from the state
 * @param {Object} state - Redux state
 * @returns {Array} Array of day objects
 */
export function getAllDays (state) {
  return selectors.all(state, ENTITY_TYPES.DAYS)
}

/**
 * Get all ranges from the state
 * @param {Object} state - Redux state
 * @returns {Array} Array of range objects
 */
export function getAllRanges (state) {
  return selectors.all(state, ENTITY_TYPES.RANGES)
}

/**
 * Get all day ranges from the state
 * @param {Object} state - Redux state
 * @returns {Array} Array of day range objects
 */
export function getAllDayRanges (state) {
  return selectors.all(state, ENTITY_TYPES.DAY_RANGES)
}

/**
 * Get day ranges for a specific day
 * @param {Object} state - Redux state
 * @param {string} dayKey - Day key (e.g., 'mon', 'tue')
 * @returns {Array} Array of day range objects for the specified day
 */
export function getDayRangesForDay (state, dayKey) {
  const dayRanges = getAllDayRanges(state)
  const days = getAllDays(state)

  // Find the day object with the matching key
  const day = days.find(d => d.day === dayKey)
  if (!day) return []

  // Filter day ranges for this day
  return dayRanges.filter(dr => dr.day === day.id)
}

/**
 * Get ranges that are scheduled for a specific day
 * @param {Object} state - Redux state
 * @param {string} dayKey - Day key (e.g., 'mon', 'tue')
 * @returns {Array} Array of range objects that are scheduled for the day
 */
export function getScheduledRangesForDay (state, dayKey) {
  const dayRanges = getDayRangesForDay(state, dayKey)
  const ranges = getAllRanges(state)

  const scheduledRangeIds = dayRanges.map(dr => dr.range)
  return ranges.filter(range => scheduledRangeIds.includes(range.id))
}

/**
 * Get ranges that are NOT scheduled for a specific day
 * @param {Object} state - Redux state
 * @param {string} dayKey - Day key (e.g., 'mon', 'tue')
 * @returns {Array} Array of range objects that are not scheduled for the day
 */
export function getUnscheduledRangesForDay (state, dayKey) {
  const scheduledRanges = getScheduledRangesForDay(state, dayKey)
  const allRanges = getAllRanges(state)

  const scheduledRangeIds = scheduledRanges.map(r => r.id)
  return allRanges.filter(range => !scheduledRangeIds.includes(range.id))
}

/**
 * Get day object by day key
 * @param {Object} state - Redux state
 * @param {string} dayKey - Day key (e.g., 'mon', 'tue')
 * @returns {Object|null} Day object or null if not found
 */
export function getDayByKey (state, dayKey) {
  const days = getAllDays(state)
  return days.find(d => d.day === dayKey) || null
}

/**
 * Check if a range is scheduled for a specific day
 * @param {Object} state - Redux state
 * @param {string} dayKey - Day key (e.g., 'mon', 'tue')
 * @param {string} rangeId - Range ID
 * @returns {boolean} True if the range is scheduled for the day
 */
export function isRangeScheduledForDay (state, dayKey, rangeId) {
  const scheduledRanges = getScheduledRangesForDay(state, dayKey)
  return scheduledRanges.some(range => range.id === rangeId)
}

/**
 * Get all ranges sorted by start time
 * @param {Object} state - Redux state
 * @returns {Array} Array of range objects sorted by start_time
 */
export function getSortedRanges (state) {
  const ranges = getAllRanges(state)
  return ranges.sort((a, b) => {
    // Convert time strings to comparable format (HH:mm:ss)
    const timeA = a.start_time || '00:00:00'
    const timeB = b.start_time || '00:00:00'
    return timeA.localeCompare(timeB)
  })
}

/**
 * Get day ranges grouped by day ID
 * @param {Object} state - Redux state
 * @returns {Object} Object with day IDs as keys and arrays of day ranges as values
 */
export function getDayRangesByDay (state) {
  const dayRanges = getAllDayRanges(state)
  const groupedByDay = {}

  dayRanges.forEach(dayRange => {
    const dayId = dayRange.day
    if (!groupedByDay[dayId]) {
      groupedByDay[dayId] = []
    }
    groupedByDay[dayId].push(dayRange)
  })

  return groupedByDay
}
