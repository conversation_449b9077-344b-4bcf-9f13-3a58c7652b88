import { SnackbarProvider, closeSnackbar } from 'notistack'
import ClearIcon from '@mui/icons-material/Clear'

import { SnackbarUtilsConfigurator } from 'utils/snack-bar'

const AUTO_HIDE_DURATION = 3000
const MAX_SNACK = 5

const Feedback = ({ children }) => {
  return (
    <SnackbarProvider
      sx={{ position: 'relative' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      autoHideDuration={AUTO_HIDE_DURATION}
      maxSnack={MAX_SNACK}
      action={(snackbarId) => (
        <ClearIcon
          sx={{
            fontSize: '20px',
            '&:hover': {
              cursor: 'pointer'
            }
          }} onClick={() => closeSnackbar(snackbarId)}
        />
      )}
    >
      <SnackbarUtilsConfigurator />
      <div>{children}</div>
    </SnackbarProvider>
  )
}

export default Feedback
