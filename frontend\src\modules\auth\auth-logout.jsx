import { connect } from 'react-redux'
import { useNavigate } from 'react-router'
import {
  Button,
  Stack,
  Divider,
  Paper,
  Box,
  useTheme
} from '@mui/material'

import { URL_PATH_AUTH_LOGIN } from 'constants/index'
import { clearState } from 'data/actions'
import Card from 'ui/card'
import { useStytch } from '@stytch/react'
import { useEffect } from 'react'

const AuthLogout = (props) => {
  const navigate = useNavigate()
  const theme = useTheme()

  const stytch = useStytch()

  useEffect(() => {
    stytch.session.revoke()
  }, [stytch])

  return (
    <Card>
      <Box
        sx={{
          padding: theme.spacing(3, 4)
        }}
      >
        <Stack
          spacing={2}
        >
          <Box component='span' sx={{ display: { xs: 'block', md: 'block' }, flexGrow: 1, textAlign: 'center' }}>
            <img src={`${window.location.origin}/logo.svg`} />
          </Box>
          <Divider />
          <Paper sx={{
            fontSize: '18px',
            fontWeight: 100,
            color: '#999999'
          }}
          >
            You have been logged out of your OnlyShipster account.
          </Paper>
          <Paper sx={{ textAlign: 'center' }}>
            <Button
              variant='contained'
              type='submit'
              onClick={() => navigate(URL_PATH_AUTH_LOGIN)}
              sx={{
                padding: '.5rem 2.5rem',
                fontWeight: 'bold'
              }}
            >
              Log in Again
            </Button>
          </Paper>
        </Stack>
      </Box>
    </Card>
  )
}

function mapDispatchToProps (dispatch) {
  return {
    logout: () => {
      dispatch(clearState())
    }
  }
}

export default connect(null, mapDispatchToProps)(AuthLogout)
