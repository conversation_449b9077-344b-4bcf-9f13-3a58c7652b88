import endsWith from 'lodash/fp/endsWith'
import isEmpty from 'lodash/fp/isEmpty'
import trimChars from 'lodash/fp/trimChars'
import getOr from 'lodash/fp/getOr'

import { AsyncError } from 'helpers/errors'

/**
 * Convert an array of error messages into a a single string
 * @param arr
 * @returns {*}
 */
function stringifyArray (arr, glue) {
  let msg = arr.join(glue)
  msg = msg.replace(/[!:.?]\./g, '.')
  msg = trimChars('[]')(msg)
  if (msg.length > 4 && msg[0] === "'" && msg[msg.length - 1] === "'") {
    msg = msg.slice(1, -1)
  }
  return msg
}

/**
 * Transform a Django REST validation error from our API format
 * to a format expected by redux-form Forms (e.g. for use in a redux-form SubmissionError)
 */
export function apiValidationToFormErrors (apiErrors, glue = '. ') {
  // Make apiErrors an obj, in case it is an array of strings
  const normApiErrors = Array.isArray(apiErrors) ? { detail: apiErrors } : apiErrors

  /* eslint-disable camelcase, no-underscore-dangle */
  const { non_field_errors, __all__, detail, message, ...formErrors } = normApiErrors

  if (non_field_errors) {
    formErrors._error = non_field_errors
  } else if (__all__) {
    formErrors._error = __all__
  } else if (detail) {
    formErrors._error = detail
  } else if (message) {
    formErrors._error = message
  }

  // normalize arrays to strings
  Object.keys(formErrors).forEach((prop) => {
    if (Array.isArray(formErrors[prop])) {
      if (Array.isArray(formErrors[prop][0])) {
        // So many arrays :(
        // Arrays can contain arrays. This occurs in nested forms where
        // a nested part of the form has a non-field error. So normalize it for
        // redux forms.
        formErrors[prop] = {
          _error: stringifyArray(formErrors[prop][0], glue)
        }
      } else if (typeof formErrors[prop][0] === 'string') {
        formErrors[prop] = stringifyArray(formErrors[prop], glue)
      }
    }
  })
  /* eslint-enable camelcase, no-underscore-dangle */

  return formErrors
}

/**
 * @class NormalizedError
 * Standardize an Axios error for easy consumption by other code.
 * This class is concerned only with building error messages, or objects
 * of error messages.
 */
export class NormalizedErrorMessage {
  /**
     * Build a normalized error message object from the incoming error.
     * error is either Axios error object, or a custom error object
     * @param {object} error An error object, as returned by Axios, or a custom error object.
     */
  constructor (error, noPreamble = false) {
    this.err = {
      message: '',
      details: null
    }
    this.flatError = false

    if (error instanceof AsyncError) {
      this.err.message = error.message || 'Error processing the request.'
    } else if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx.

      const preamble = noPreamble ? '' : this.getStatusPreamble(error)

      // Parse validation errors to support field-level form errors.
      // Otherwise construct a reasonable error message;
      // Handle 409 status code as well, as that is sent during, and can contain a validation error as a payload.
      if (error.response.status === 400 || error.response.status === 409) {
        if (error.response.data) {
          // Defensive handling for response data, in case it is malformed
          try {
            this.err.details = apiValidationToFormErrors(error.response.data)
          } catch (e) {
            this.err.details = {}
          }
        }

        // Ensure we have some error, so a form can show something
        if (isEmpty(this.err.details)) {
          this.err.details = {
            _error: 'Error submitting form'
          }
        }
      } else if (error.response.data) {
        if (typeof error.response.data === 'string') {
          // Raw HTML typically indicates a 500 error, or other 5XX server
          if (error.response.data.includes('</')) {
            // 806 is to aid support/debugging in case an end user reports this error.
            this.err.message = `${preamble}If error persists, please contact support (error code 806).`
          } else {
            this.err.message = `${preamble}${error.response.data}`
          }
        } else if (typeof error.response.data === 'object') {
          let msg = ''
          if (error.response.data.detail) {
            msg = error.response.data.detail
          } else {
            msg = NormalizedErrorMessage.flattenErrorObject(error.response.data)
          }
          this.err.message = `${preamble}${msg}`
        } else if (Array.isArray(error.response.data)) {
          let msg = ''
          if (error.response.data.detail) {
            msg = error.response.data.detail
          } else {
            msg = NormalizedErrorMessage.flattenErrorObject(error.response.data)
          }
          this.err.message = `${preamble}${msg}`
        }
      } else if (error.message) {
        this.err.message = `${preamble}${error.message}`
      }
    } else if (error.request) {
      // The request was made but no response was received
      const msg = error.message ? ` ${error.message}` : ''
      this.err.message = `No response received from server. Please try again.${msg}`
    }

    // catchall if an error snuck through with no message
    if (!this.err.message) {
      /* eslint-disable camelcase, no-underscore-dangle */

      // prefer a custom error message from the server, if there was one
      if (getOr(false, 'details._error')(this.err)) {
        this.err.message = this.err.details._error
      } else if (error.message) {
        this.err.message = error.message
        if (noPreamble) {
          this.err.message = error.message
        } else {
          this.err.message = `Error communicating with server. ${error.message}`
        }
      } else {
        this.err.message = 'Error communicating with server'
      }
      /* eslint-enable no-underscore-dangle */
    }
  }

  getStatusPreamble (error) {
    let preamble = 'Error'

    if (error.response && error.response.status) {
      switch (error.response.status) {
        case 301:
        case 302:
          preamble = 'Resource moved'
          break
        case 304:
          preamble = 'Resource not modified'
          break
        case 401:
          preamble = 'Authorization error'
          break
        case 403:
          preamble = 'Permission error'
          break
        case 404:
          preamble = 'Resource not found'
          break
        case 405:
          preamble = 'Action not allowed on this resource'
          break
        case 500:
          preamble = 'Server Error'
          break
        case 501:
          preamble = 'Service cannot handle request'
          break
        case 502:
          preamble = 'Bad gateway'
          break
        case 503:
          preamble = 'Service temporarily unavailable'
          break
        case 504:
          preamble = 'Server Timeout. Please try again'
          break
        default:
          preamble = `Error (${error.response.status})`
      }
    }

    return `${preamble}: `
  }

  /**
     * Get an error object for redux forms Forms
     * @returns {object} An object appropriate to be thrown as SubmissionError for redux forms.
     */
  asFormErrorObj () {
    return this.err.details || { _error: this.err.message }
  }

  /**
     * Get an error string for feedback message
     * @return {string} A string of error messages, without form name.
     */
  asFormFeedback () {
    if (!this.err.details) return ''
    return Object.values(this.err.details).join(' ')
  }

  /**
     * @returns {string} A string with error messages combined into a single string.
     */
  asFlatError () {
    if (!this.flatError) {
      if (this.err.details) {
        this.flatError = NormalizedErrorMessage.flattenErrorObject(this.err.details)
      } else {
        this.flatError = this.err.message
      }
    }

    return this.flatError
  }

  /**
     * Flatten an object of error messages into a string. Object has keys that
     * identify the field the error is associated with or that it is a general error.
     * Object values are error messages, as strings. Values should not be objects
     * or arrays.
     *
     * @param errObj An object of error messages, without nested objects
     * @param glue
     */
  static flattenErrorObject (
    errObj,
    glue = '\n',
    specialKeys = ['_error', 'detail', 'non_field_errors'],
    ignoreKeys = ['code']
  ) {
    let message = ''

    // Put special keys first
    if (specialKeys) {
      message = Object.keys(errObj).filter(key => specialKeys.includes(key))
        .reduce((msg, key) => `${msg}${errObj[key]}${glue}`, message)
    }

    message = Object.keys(errObj).filter(key => !specialKeys.includes(key) && !ignoreKeys.includes(key))
      .reduce((msg, key) => `${msg}${key}: ${errObj[key]}${glue}`, message)

    return message.replace(/\n$/g, '')
  }
}
