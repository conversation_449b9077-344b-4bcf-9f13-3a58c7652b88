# SHIPTBOT

Simple Setup:

1. Run `init_new_database.sh`

```shell
cp env.dev_base .env_dev
```

```shell
chmod +x ./init_new_database.sh
./init_new_database.sh
```

2. Docker

```shell
# Makefile
make up
```

Or if you want to use `sbdc` command
Add this code to `.zsh-aliases`

```shell
export SHIPTBOT_PROJECT_DIR="~/shiptbot/projects/shiptbot" # change it if your path is different.
alias sbdc="docker-compose -f ${SHIPTBOT_PROJECT_DIR}/docker-compose.yml -f ${SHIPTBOT_PROJECT_DIR}/compose/dev.yml"
alias sbbuild="sbdc build"
alias sbup="sbdc up"
alias sbrestart="sbdc restart"
alias sbdown="sbdc down"
alias sbshell="sbdc exec django python manage.py shell"
alias sbtest="sbdc exec django pytest --nomigrations"
alias sbtestv="sbdc exec django pytest --nomigrations --log-cli-level info"
```

Now you can use `sbdc` command

3. Frontend

```shell
cd frontend
touch .env
```
