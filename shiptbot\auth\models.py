import arrow

from shiptbot.models.user.models import User


class BaseAuthentication:
    ISSUER = None
    AUDIENCE = None
    AUTH_HEADER_PREFIX = "Bearer"

    def __init__(self):
        """Initialize IDP client"""
        self.client = None

    @property
    def ready(self):
        return self.client is not None

    def jwt_decode(self, token):
        raise NotImplementedError()

    def get_auth_username_from_payload(self, payload):
        return payload.get("sub")

    def get_jwks(self):
        raise NotImplementedError()

    def create_member(self, user: User):
        raise NotImplementedError()

    def send_password_reset_email(self, user: User):
        raise NotImplementedError()

    def get_member(self, email):
        raise NotImplementedError()

    def get_user_from_auth_username(self, auth_username):
        raise NotImplementedError()

    def update_member_last_login(self, user: User, timestamp=None):
        if not timestamp:
            timestamp = arrow.utcnow().timestamp()

        user.last_login = arrow.get(timestamp).datetime
        user.save()

        return user

    def send_sso_invitation_email(
        self, user: User, redirect_url=None, sender_name=None
    ):
        raise NotImplementedError()
        # get_adapter().send_sso_invitation_email(user, redirect_url, sender_name)

    def can_send_magic_link_email(self, user: User):
        return True

    def can_send_password_reset_email(self, user: User):
        return True

    def is_jit_enabled(self, organization):
        return False
