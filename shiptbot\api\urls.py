from django.urls import include, re_path
from rest_framework.routers import SimpleRouter

from .views.shiptbot import (
    BotSettingViewSet,
    DayViewSet,
    OfferViewSet,
    RangeViewSet,
    ShiptAuthViewSet,
    ShiptBotViewSet,
    StoreViewSet,
    auth0_shipt,
)
from .views.subscription import PlanViewSet, SubscriptionViewSet

router = SimpleRouter()

router.register(r"sauth", ShiptAuthViewSet, basename="shiptauth")
router.register(r"botsettings", BotSettingViewSet, basename="botsetting")
router.register(r"stores", StoreViewSet, basename="store")
router.register(r"days", DayViewSet, basename="day")
router.register(r"ranges", RangeViewSet, basename="range")
router.register(r"offers", OfferViewSet, basename="offer")
router.register(r"subscription", SubscriptionViewSet, basename="subscription")
router.register(r"plans", PlanViewSet, basename="plan")
router.register(r"sb", ShiptBotViewSet, basename="shiptbot")

urlpatterns = [
    re_path(r"^", include(router.urls)),
    re_path(r"^auth/", include("shiptbot.auth.urls")),
    re_path(r"^auth0/shipt/?$", auth0_shipt, name="auth0_shipt"),
]
