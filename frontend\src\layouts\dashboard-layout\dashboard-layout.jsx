import { useState, useEffect } from 'react'
import {
  AppBar,
  Box,
  CssBaseline,
  Toolbar,
  useMediaQuery,
  useTheme
} from '@mui/material'
import { Outlet, useLocation } from 'react-router-dom'

import { childrenRoutes as dashboardRoutes } from 'routes/dashboard-routes'

import Header from './header'
import Main from './main'
import Sidebar from './sidebar'
import MobileBottomNavigation from './mobile-bottom-navigation'

const DashboardLayout = () => {
  const theme = useTheme()
  const location = useLocation()

  const [leftDrawerOpened, setLeftDrawerOpened] = useState(true)
  // eslint-disable-next-line no-undef
  const [currentPage, setCurrentPage] = useState(localStorage.getItem('currentPage'))
  const matchDownMd = useMediaQuery(theme.breakpoints.down('md'))

  const handleLeftDrawerToggle = () => {
    setLeftDrawerOpened(!leftDrawerOpened)
  }

  useEffect(() => {
    const currentPage = dashboardRoutes.find(item => item.path === location.pathname)
    let title = 'OnlyShipster '

    if (currentPage && currentPage.title) {
      title += ` | ${currentPage.title}`
    }
    document.title = title
  }, [location])

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <CssBaseline />
      <AppBar
        enableColorOnDark
        position='fixed'
        color='inherit'
        elevation={0}
        sx={{
          bgcolor: theme.palette.background.default,
          transition: leftDrawerOpened ? theme.transitions.create('width') : 'none'
        }}
      >
        <Toolbar
          sx={{
            minHeight: { xs: '56px', sm: '64px' },
            height: { xs: '56px', sm: '64px' },
            paddingY: { xs: '8px', sm: '16px' }
          }}
        >
          <Header handleLeftDrawerToggle={handleLeftDrawerToggle} currentPage={currentPage} />
        </Toolbar>
      </AppBar>

      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {!matchDownMd && (
          <Sidebar
            drawerOpen={leftDrawerOpened}
            drawerToggle={handleLeftDrawerToggle}
            setCurrentPage={setCurrentPage}
          />
        )}
        <Main theme={theme} open={!matchDownMd ? leftDrawerOpened : false}>
          <Outlet />
        </Main>
      </Box>

      {matchDownMd && (
        <MobileBottomNavigation setCurrentPage={setCurrentPage} />
      )}
    </Box>
  )
}

export default DashboardLayout
