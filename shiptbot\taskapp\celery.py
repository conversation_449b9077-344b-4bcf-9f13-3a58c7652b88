import logging
import os

from celery import Celery
from django.apps import AppConfig, apps
from django.conf import settings

if not settings.configured:
    # set the default Django settings module for the 'celery' program.
    os.environ.setdefault(
        "DJANGO_SETTINGS_MODULE", "config.settings.production"
    )  # pragma: no cover


app = Celery("shiptbot")


class CeleryConfig(AppConfig):
    name = "shiptbot.taskapp"
    verbose_name = "Celery Config"

    def ready(self):
        # Using a string here means the worker will not have to
        # pickle the object when using Windows.
        app.config_from_object("django.conf:settings", namespace="CELERY")
        installed_apps = [app_config.name for app_config in apps.get_app_configs()]
        app.autodiscover_tasks(lambda: installed_apps, force=True)

        # Sentry Integrations
        if hasattr(settings, "SENTRY_CONFIG"):
            import sentry_sdk
            from sentry_sdk.integrations.celery import CeleryIntegration
            from sentry_sdk.integrations.django import DjangoIntegration
            from sentry_sdk.integrations.logging import LoggingIntegration

            sentry_sdk.init(
                dsn=settings.SENTRY_CONFIG["DSN"],
                integrations=[
                    CeleryIntegration(),
                    DjangoIntegration(),
                    LoggingIntegration(event_level=logging.WARNING),
                ],
                environment=settings.ENV_LABEL,
                traces_sample_rate=settings.SENTRY_CONFIG["SENTRY_TRACES_SAMPLE_RATE"],
                profiles_sample_rate=settings.SENTRY_CONFIG[
                    "SENTRY_PROFILES_SAMPLE_RATE"
                ],
            )
