import { useState } from 'react'
import { <PERSON>, But<PERSON>, Grid2, Container, Typography } from '@mui/material'
import API from 'data/api'
import { PasswordElement, TextFieldElement, useForm } from 'react-hook-form-mui'
import { connect } from 'react-redux'

import ShiptMFAVerification from './shipt-mfa-verification'

const ShiptLogin = props => {
  const { sendMfa, verifyMfa } = props

  const [mfaData, setMfaData] = useState(null)

  const { control, handleSubmit, watch } = useForm({
    username: '',
    password: ''
  })

  const onSubmit = data => {
    sendMfa(data).then(res => setMfaData(res))
  }
  return (
    <Container
      maxWidth='md'
      sx={{
        py: { xs: 2, sm: 3 },
        px: { xs: 2, sm: 3 }
      }}
    >
      <Box
        sx={{
          backgroundColor: 'white',
          borderRadius: 2,
          padding: { xs: 2, sm: 3 },
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e0e0e0',
          width: '100%'
        }}
      >
        {/* Header */}
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Typography
            variant='h4'
            component='h1'
            sx={{
              fontWeight: 'bold',
              color: '#333',
              mb: 1
            }}
          >
            Shipt Login
          </Typography>
          <Typography
            variant='body1'
            color='text.secondary'
            sx={{
              fontSize: '1rem',
              lineHeight: 1.5
            }}
          >
            You need to log in to your Shipt account to sync your data.
          </Typography>
        </Box>
        {mfaData
          ? (
            <ShiptMFAVerification mfaData={mfaData} verifyMfa={verifyMfa} />
            )
          : (
            <Box component='form' onSubmit={handleSubmit(onSubmit)}>
              <Grid2 container spacing={2}>
                <Grid2 item size={12}>
                  <TextFieldElement fullWidth label='Username' name='username' control={control} required />
                </Grid2>
                <Grid2 item size={12}>
                  <PasswordElement fullWidth label='Password' name='password' control={control} required />
                </Grid2>
                <Grid2 item size={12}>
                  <Button variant='contained' fullWidth color='primary' type='submit' sx={{ padding: '.5rem 2.5rem' }}>
                    Submit
                  </Button>
                </Grid2>
              </Grid2>
            </Box>
            )}
      </Box>
    </Container>
  )
}

const mapDispatchToProps = dispatch => {
  return {
    sendMfa: (data, options = {}) => dispatch(API.shiptauth.sendMfa(data, options)),
    verifyMfa: (data, options = {}) => dispatch(API.shiptauth.verifyMfa(data, options))
  }
}

export default connect(null, mapDispatchToProps)(ShiptLogin)
