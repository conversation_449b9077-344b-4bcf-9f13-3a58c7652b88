AUTH_HEADERS_BASE = {
    "content-type": "application/x-www-form-urlencoded",
    "x-shipt-deviceid": "7006BE60-E57F-4717-856E-7A112D6B4304",
    "accept": "*/*",
    "x-user-type": "Driver",
    "accept-language": "en-US,en;q=0.9",
    "accept-encoding": "gzip, deflate, br",
    "x-shipt-geo-last-tracked": "2025-07-03T23:49:18.804Z",
    "x-shipt-geo-mocked": "false",
    "user-agent": "ShiptShopper/2227420 CFNetwork/3826.500.131 Darwin/24.5.0",
    "x-shipt-identifier": "neutron-ios-4.98.0-8320",
}

OFFER_HEADERS_BASE = {
    "content-type": "application/json",
    "x-shipt-deviceid": "7006BE60-E57F-4717-856E-7A112D6B4304",
    "accept": "*/*",
    "accept-language": "en-US,en;q=0.9",
    "accept-encoding": "gzip, deflate, br",
    "x-shipt-geo-last-tracked": "2025-07-05T02:04:00.990Z",
    "x-shipt-geo-mocked": "false",
    "user-agent": "ShiptShopper/2227420 CFNetwork/3826.500.131 Darwin/24.5.0",
    "x-shipt-identifier": "neutron-ios-4.98.0-8320",
    "cookie": "dtCookie=v_4_srv_6_sn_0B8CB6A5277F98EAEF654B9DB1A386A5_perc_100000_ol_0_mul_1_app-3Aea7c4b59f27d43eb_1_rcs-3Acss_0",
    "content-length": "0",
}
