import { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { Container, Box, Tabs, Tab } from '@mui/material'

import API from 'data/api'
import { ZoneSearch } from './components'
import SearchSettings from './components/search-settings'

function TabPanel ({ children, value, index, ...other }) {
  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 1 }}>
          {children}
        </Box>
      )}
    </div>
  )
}

function a11yProps (index) {
  return {
    id: `settings-tab-${index}`,
    'aria-controls': `settings-tabpanel-${index}`
  }
}

const BotSettingsScreen = (props) => {
  const { listStores, listBotSettings } = props
  const [tabValue, setTabValue] = useState(0)

  useEffect(() => {
    listStores()
    listBotSettings()
  }, [listStores, listBotSettings])

  const handleTabChange = (_, newValue) => {
    setTabValue(newValue)
  }

  return (
    <Container maxWidth='md' sx={{ py: 3 }}>
      <Box
        sx={{
          backgroundColor: 'white',
          borderRadius: 2,
          padding: { xs: 2, sm: 3 },
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e0e0e0',
          width: '100%'
        }}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label='bot settings tabs'>
            <Tab label='Zone Settings' {...a11yProps(0)} />
            <Tab label='Search Setting' {...a11yProps(1)} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <ZoneSearch />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <SearchSettings />
        </TabPanel>
      </Box>
    </Container>
  )
}

const mapDispatchToProps = dispatch => {
  return {
    listStores: () => dispatch(API.stores.list()),
    listBotSettings: () => dispatch(API.botsettings.list())
  }
}

const ConnectedBotSettingsScreen = connect(null, mapDispatchToProps)(BotSettingsScreen)
ConnectedBotSettingsScreen.displayName = 'BotSettingsScreen'

export default ConnectedBotSettingsScreen
