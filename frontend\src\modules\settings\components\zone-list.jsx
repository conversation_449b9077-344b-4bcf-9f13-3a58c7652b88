import React from 'react'
import { Box, Typography, CircularProgress, Button } from '@mui/material'
import { connect } from 'react-redux'

import { selectors as storeSelectors } from 'data/stores'
import ZoneListItem from './zone-list-item'
import API from 'data/api'

const ZoneList = ({ storesByZone, loading, refreshStores }) => {
  const zones = Object.values(storesByZone)

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  if (zones.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Button
          variant='contained'
          size='large'
          onClick={refreshStores}
          sx={{
            mb: 2,
            px: 4,
            py: 1.5
          }}
        >
          Refresh Zones
        </Button>
        <Typography variant='h6' color='text.secondary'>
          No stores found
        </Typography>
        <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
          Please refresh your zones and stores to see available locations.
        </Typography>
      </Box>
    )
  }

  return (
    <Box>
      {/* Header with text and button on same line */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 },
        mb: 3
      }}
      >
        <Typography variant='body1' color='text.secondary' sx={{ flex: 1 }}>
          Configure your store preferences for each zone. Enable stores and set minimum pay and distance requirements.
        </Typography>
        <Button
          variant='contained'
          size='large'
          onClick={refreshStores}
          sx={{
            px: 4,
            py: 1.5,
            flexShrink: 0
          }}
        >
          Refresh Zones
        </Button>
      </Box>

      {zones.map((zone) => (
        <ZoneListItem key={zone.zone_id} zone={zone} />
      ))}
    </Box>
  )
}

const mapStateToProps = (state) => ({
  storesByZone: storeSelectors.getStoresByZone(state),
  loading: false // You can add loading state from your async actions if needed
})

const mapDispatchToProps = dispatch => ({
  refreshStores: () => dispatch(API.stores.refresh())
})

export default connect(mapStateToProps, mapDispatchToProps)(ZoneList)
