import { TextFieldElement } from 'react-hook-form-mui'

const TextFieldSmart = ({ name, data, errors, control, ...otherProps }) => {
  const required = { ...otherProps }.rules.required

  return (
    <TextFieldElement
      name={name}
      label={`${data.title}${required ? ' *' : ''}`}
      control={control}
      variant='outlined'
      fullWidth
      error={!!errors[name]}
      margin='normal'
      helperText={errors[name] ? errors[name].message : ''}
      {...otherProps}
    />
  )
}

export default TextFieldSmart
