import { StrictMode } from 'react'
import getOr from 'lodash/fp/getOr'
import isEqual from 'lodash/fp/isEqual'

import '@fontsource/roboto/300.css'
import '@fontsource/roboto/400.css'
import '@fontsource/roboto/500.css'
import '@fontsource/roboto/700.css'

import '@fontsource/inter/400.css'
import '@fontsource/inter/500.css'
import '@fontsource/inter/600.css'
import '@fontsource/inter/700.css'

import '@fontsource/poppins/400.css'
import '@fontsource/poppins/500.css'
import '@fontsource/poppins/600.css'
import '@fontsource/poppins/700.css'

import 'assets/styles/style.scss'
import DashboardApp from 'apps/dashboard/app'

import './App.css'

const USE_STRICT_MODE = 'VITE_REACT_APP_USE_STRICT_MODE'

function App () {
  const strictModeEnabled = isEqual('true', getOr('', USE_STRICT_MODE)(import.meta.env))

  return strictModeEnabled
    ? (
      <StrictMode>
        <DashboardApp />
      </StrictMode>
      )
    : (
      <DashboardApp />
      )
}

export default App
