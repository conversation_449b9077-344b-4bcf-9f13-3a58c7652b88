export const AUTH_HEADER_PREFIX_STYTCH = 'Bearer'

export const STYTCH_SESSION_JWT_COOKIE_NAME = 'stytch_session_jwt'
export const STYTCH_SESSION_TOKEN_COOKIE_NAME = 'stytch_session'

export const ONE_WEEK_MINUTES = 60 * 24 * 7

export const ONE_DAY_MINUTES = 60 * 24

export const STYTCH_AUTH_ERROR = {
  INVALID_ORGANIZATION: 'invalid_organization_id',
  INVALID_PASSWORD_RESET_TOKEN: 'unable_to_auth_password_reset_token',
  INVALID_PASSWORD: 'unauthorized_credentials',
  LINK_EXPIRED: 'unable_to_auth_magic_link',
  OAUTH_NOT_ALLOWED: 'invalid_intermediate_session_token_for_organization',
  INVALID_JIT_PROVISIONING: 'invalid_connection_for_jit_provisioning',
  TOO_MANY_REQUESTS: 'too_many_requests',

  // MFA Error Codes
  INVALID_OTP: 'otp_code_not_found',
  INVALID_TOTP: 'unable_to_authenticate_totp',
  INVALID_RECOVERY_CODE: 'recovery_codes_not_found',
  INVALID_PHONE_NUMBER: 'invalid_phone_number',
  TOTP_ALREADY_AUTHENTICATED: 'totp_code_already_authenticated',

  // Custom error codes
  AUTH_METHOD_NOT_ALLOWED: 'auth_method_not_allowed',
  AUTH_USER_NOT_FOUND: 'auth_user_not_found'
}

/**
 * Custom Stytch Error Code - for display purposes
 */
export const STYTCH_ERROR_CODE = {
  [STYTCH_AUTH_ERROR.INVALID_ORGANIZATION]: 'organization_not_found',
  [STYTCH_AUTH_ERROR.INVALID_PASSWORD_RESET_TOKEN]: 'invalid_password_reset_token',
  [STYTCH_AUTH_ERROR.INVALID_PASSWORD]: 'invalid_password',
  [STYTCH_AUTH_ERROR.LINK_EXPIRED]: 'link_used_or_expired',
  [STYTCH_AUTH_ERROR.OAUTH_NOT_ALLOWED]: 'oauth_not_allowed',
  [STYTCH_AUTH_ERROR.AUTH_METHOD_NOT_ALLOWED]: 'auth_method_not_allowed',
  [STYTCH_AUTH_ERROR.AUTH_USER_NOT_FOUND]: 'auth_user_not_found',
  [STYTCH_AUTH_ERROR.INVALID_JIT_PROVISIONING]: 'jit_provisioning_not_allowed'
}
