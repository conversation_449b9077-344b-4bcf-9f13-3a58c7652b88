
# OSX
.DS_Store
.AppleDouble
.LSOverride
Thumbs.db

# Yarn / NPM
node_modules/
node_modules_*/
# yarn.lock and package.json should only be in frontend*/ sub-directories, not root
/yarn.lock
/package.json
# fnm node version file
.nvmrc

# Caches
.cache/

# Emacs
.~

# SQLite database files
*.sqlite

# SublimeText
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
# proportion of contributors will probably not be using SublimeText
# *.sublime-project

# SFTP configuration file
sftp-config.json

# PYENV
.python-version

# Basics
*.py[cod]
__pycache__

# Logs
*.log
pip-log.txt

# Docs
docs/build/*
docs/**/_build
!docs/build/.gitkeep

# Unit test / coverage reports
.coverage
.tox
nosetests.xml
htmlcov
.pytest_cache/
test-reports/

# Translations
*.mo
*.pot

# Pycharm
.idea/*
*/.idea/*

# VScode
.vscode/*
*/.vscode/*

# Vim

*~
*.swp
*.swo

# testing / coverage artifacts
frontend/coverage/

# Compass
.sass-cache

# Env file(s)
.env*

# User-uploaded media
signasure/media/*

# exception for our testdata, which
# is more convenient to put in media/
!signasure/media/testdata/
signasure/media/testdata/*_*

# exception for sampledata, which is used to bootstrap accounts
!signasure/media/sampledata/
signasure/media/sampledata/*_*

# MailHog binary
mailhog

# static files we don't care about
signasure/static/**/*.css.map
signasure/static/dev/

# ignore semantic ui files / themes we do not need
signasure/static/ui/semantic.js
signasure/static/ui/semantic.min.js
signasure/static/ui/themes/basic/
signasure/static/ui/themes/github/
signasure/static/ui/themes/material/

# ignore symlinks / copied files required for local dev
staticfiles/
!staticfiles/geoip

# ignore celerybeat
celerybeat-schedule
celerybeat.pid

__tmp*

# visual studio files
.vs/

# prettier js format
.prettierrc.*

# cypress env
cypress.env.*
frontend/cypress/downloads/

# browserstack logs / results
frontend/results/
frontend/log/

playground
