import { RestAuthHelper } from 'data/helpers/rest'

import { shiptAuthAsyncActions } from './actions'

class ShiptAuthRest extends RestAuthHelper {
  get defaultOptions () {
    return {
      verbs: ['sendMfa', 'verifyMfa']
    }
  }

  sendMfa (data, options = {}) {
    return this.requestThunk({
      method: 'post',
      url: this.buildUrl('/send_mfa/'),
      actions: shiptAuthAsyncActions('sendMfa'),
      data,
      options
    })
  }

  verifyMfa (data, options = {}) {
    return this.requestThunk({
      method: 'post',
      url: this.buildUrl('/verify_mfa/'),
      actions: shiptAuthAsyncActions('verifyMfa'),
      data,
      options
    })
  }
}

export default ShiptAuthRest
