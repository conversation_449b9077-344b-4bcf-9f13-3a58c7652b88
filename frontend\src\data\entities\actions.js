import { createAction } from 'redux-actions'

export const update = createAction('entities/UPDATE')
export const remove = createAction('entities/REMOVE', (entity, id) => ({ entity, id }))
export const removeAll = createAction('entities/REMOVE_ALL', toDelete => ({ toDelete }))
export const clear = createAction('entities/CLEAR_ENTITY', entityType => ({ entityType }))

export const optimisticUpdate = createAction(
  'entities/OPT_UPDATE',
  (entityType, entityId, data, partial = false) => ({ entityType, entityId, data, partial })
)

export const optimisticRollback = createAction(
  'entities/OPT_ROLLBACK',
  (entityType, entityId) => ({ entityType, entityId })
)

export const optimisticClear = createAction(
  'entities/OPT_CLEAR',
  (entityType, entityId) => ({ entityType, entityId })
)
