import { useNavigate } from 'react-router'

import HomeTwoToneIcon from '@mui/icons-material/HomeTwoTone'
import { Button, Card, CardContent, CardMedia, Grid, Typography } from '@mui/material'
import { styled } from '@mui/material/styles'

import imageError from 'assets/images/img-error-500.svg'

// styles
const CardMediaWrapper = styled('div')({
  maxWidth: '396px',
  margin: '0 auto',
  position: 'relative'
})

const ErrorWrapper = styled('div')({
  maxWidth: '600px',
  margin: '0 auto',
  textAlign: 'center'
})

const ErrorCard = styled(Card)({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
})

// ==============================|| ERROR PAGE ||============================== //

const UncaughtErrorPage = () => {
  const navigate = useNavigate()

  return (
    <ErrorCard>
      <CardContent>
        <Grid container justifyContent='center' spacing={2}>
          <Grid item xs={12}>
            <CardMediaWrapper>
              <CardMedia
                component='img'
                image={imageError}
              />
            </CardMediaWrapper>
          </Grid>
          <Grid item xs={12}>
            <ErrorWrapper>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant='h1' component='div'>
                    Internal Server Error
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant='body2'>
                    Server error 500. we fixing the problem. please try again at a later stage.{' '}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant='contained'
                    size='large'
                    onClick={() => {
                      navigate(-1)
                    }}
                  >
                    <HomeTwoToneIcon sx={{ fontSize: '1.3rem', mr: 0.75 }} /> Home
                  </Button>
                </Grid>
              </Grid>
            </ErrorWrapper>
          </Grid>
        </Grid>
      </CardContent>
    </ErrorCard>
  )
}

export default UncaughtErrorPage
