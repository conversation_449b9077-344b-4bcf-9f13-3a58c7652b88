#!/usr/bin/env bash
python manage.py collectstatic --noinput
python manage.py migrate
python manage.py create_test_account --company "preview" --email "<EMAIL>" --first-name "Preview" --last-name "User" --password $PREVIEW_PASSWORD --force
echo "from django.contrib.auth import get_user_model;User = get_user_model();User.objects.create_superuser('admin', '<EMAIL>', '$PREVIEW_PASSWORD') if not User.objects.filter(username='admin') else None" | python manage.py shell
