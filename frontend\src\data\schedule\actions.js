import { createAsyncActionCreators } from 'data/helpers/actions'

/**
 * Standard async action creators for auth REST api calls
 * @type {function(*)}
 */
export const daysAsyncActions = createAsyncActionCreators('days')

/**
 * Standard async action creators for auth REST api calls
 * @type {function(*)}
 */
export const rangesAsyncActions = createAsyncActionCreators('ranges')

/**
 * Standard async action creators for auth REST api calls
 * @type {function(*)}
 */
export const dayRangesAsyncActions = createAsyncActionCreators('dayranges')
