import { useSnackbar } from 'notistack'

/*
  This utility component facilitates the injection of Snackbar functionality wherever it's imported,
  currently at the root of the Dashboard app.
  By utilizing this component, the Dashboard App can easily call 'enqueueSnackbar' from anywhere,
  both within and outside of components. Previously, it wasn't possible to call enqueueSnackbar()
  from plain JavaScript.

  There are 2 ways to show a snackbar:

  ********* First Way: use snackActions **********
  dispatch(API.auth.login())
    .then(() => {
      snackActions.success("Login Successfully")
    })
    .catch((error) => {
      snackActions.error("Login failed")
    })
  ********* First Way **********

  ********* Second Way: use notiOnSuccess **********
  const mapDispatchToProps = (dispatch, ownProps) => {
    return {
      onInit: () => notiOnSuccess(
        dispatch(API.auth.login()),
        'Successfully Logged In' #
      ),
      onSubmitSurvey: (data) => dispatch(API.accounts.submitOnboardSurvey(data))
    }
  }

  ***NOTE***
  notiOnFailed wasn't declared because error messages
  are automatically caught and displayed in rest.js and rest-resource.js.
  Additionally, not all successful requests should trigger a success message.
  ********* Second Way **********
*/
const InnerSnackbarUtilsConfigurator = (props) => {
  props.setUseSnackbarRef(useSnackbar())
  return null
}

let useSnackbarRef
const setUseSnackbarRef = (useSnackbarRefProp) => {
  useSnackbarRef = useSnackbarRefProp
}

export const SnackbarUtilsConfigurator = () => {
  return <InnerSnackbarUtilsConfigurator setUseSnackbarRef={setUseSnackbarRef} />
}

export const snackActions = {
  success (msg) {
    this.toast(msg, 'success')
  },
  warning (msg) {
    this.toast(msg, 'warning')
  },
  info (msg) {
    this.toast(msg, 'info')
  },
  error (msg) {
    this.toast(msg, 'error')
  },
  toast (msg, variant = 'default') {
    useSnackbarRef.enqueueSnackbar(msg, { variant })
  }
}

export const notiOnSuccess = async (promise, message) => {
  try {
    const res = await promise
    snackActions.success(message)
    return res
  } catch (error) {
    console.error(error)
    throw error
  }
}
