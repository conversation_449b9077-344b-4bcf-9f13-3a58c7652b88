import { AuthFlowType, B2BProducts, B2BOAuthProviders } from '@stytch/vanilla-js'

import { stytchHelper } from './helpers'

export const OrganizationConfig = {
  authFlowType: AuthFlowType.Organization,
  products: [B2BProducts.emailMagicLinks, B2BProducts.oauth],
  emailMagicLinksOptions: {
    loginRedirectURL: stytchHelper.loginSignupRedirectUrl,
    signupRedirectURL: stytchHelper.loginSignupRedirectUrl
  },
  oauthOptions: {
    providers: [
      { type: B2BOAuthProviders.Google, one_tap: true }
    ],
    loginRedirectURL: stytchHelper.loginSignupRedirectUrl,
    signupRedirectURL: stytchHelper.loginSignupRedirectUrl
  },
  sessionOptions: {
    sessionDurationMinutes: stytchHelper.sessionDurationMinutes
  }
}

export const DiscoveryConfig = {
  authFlowType: AuthFlowType.Discovery,
  products: [B2BProducts.oauth, B2BProducts.emailMagicLinks],
  emailMagicLinksOptions: {
    discoveryRedirectURL: stytchHelper.loginSignupRedirectUrl
  },
  oauthOptions: {
    providers: [{ type: B2BOAuthProviders.Google, one_tap: true }],
    discoveryRedirectURL: stytchHelper.loginSignupRedirectUrl
  },
  sessionOptions: {
    sessionDurationMinutes: stytchHelper.sessionDurationMinutes
  }
}

export const styles = {
  container: {
    backgroundColor: '#FFFFFF',
    borderColor: '#ADBCC5',
    borderRadius: '8px',
    width: '450px'
  },
  colors: {
    primary: '#19303D',
    secondary: '#5C727D',
    success: '#0C5A56',
    error: '#8B1214'
  },
  buttons: {
    primary: {
      backgroundColor: '#3553A4',
      textColor: '#FFFFFF',
      borderColor: '#3553A4',
      borderRadius: '4px'
    },
    secondary: {
      backgroundColor: '#FFFFFF',
      textColor: '#19303D',
      borderColor: '#19303D',
      borderRadius: '4px'
    }
  },
  inputs: {
    backgroundColor: '#FFFFFF00',
    borderColor: '#19303D',
    borderRadius: '4px',
    placeholderColor: '#8296A1',
    textColor: '#19303D'
  },
  fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
  hideHeaderText: false,
  logo: {
    logoImageUrl: `${window.location.origin}/logo.svg`
  }
}

export const callbacks = {
  onEvent: ({ type, data }) => {
    console.log(type, data)
  },
  onError: (data) => {
    console.log(data)
  }
}
