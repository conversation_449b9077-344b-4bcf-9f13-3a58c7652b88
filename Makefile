# Run `make <command>`
up:
	docker compose -f docker-compose.yml -f compose/dev.yml up
up-build:
	docker compose -f docker-compose.yml -f compose/dev.yml up --build
down:
	docker compose down
restart:
	docker compose restart
ps:
	docker compose ps
shell:
	docker compose -f docker-compose.yml -f compose/dev.yml exec django python manage.py shell
export_constants:
	docker compose -f docker-compose.yml -f compose/dev.yml exec django python manage.py export_constants
migrate:
	docker compose -f docker-compose.yml -f compose/dev.yml exec django python manage.py migrate
makemigrations:
	docker compose -f docker-compose.yml -f compose/dev.yml exec django python manage.py makemigrations
