import { createAsyncActionCreators } from 'data/helpers/actions'
import { createAction } from 'redux-actions'

/**
 * Standard async action creators for offers REST api calls
 * @type {function(*)}
 */
export const offersAsyncActions = createAsyncActionCreators('offers')

/**
 * Action to update offers from open metro API response
 */
export const updateOpenMetroOffers = createAction('offers/UPDATE_OPEN_METRO_OFFERS')
