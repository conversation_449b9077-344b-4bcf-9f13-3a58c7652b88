# Base env file for dev.yml docker setup
# DO NOT put API keys or other credentials in here, as this file is checked in to git

# Note that these values are copied into the Docker container when it is
# built not each time it is run. So you will need to run, e.g. `docker-compose build ...`
# to pick up any changes

DJ<PERSON><PERSON><PERSON>_SETTINGS_MODULE=config.settings.dev
DJANGO_READ_DOTENV=True
DJANGO_DOTENV_FILE=.env_dev
CELERY_USE_REDIS=True

REDIS_URL=redis://redis:6379
CELERY_BROKER_URL=redis://redis:6379
