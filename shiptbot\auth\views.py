from rest_framework.generics import RetrieveAPIView

from shiptbot.models.user.models import User
from shiptbot.services.stripe.helpers import StripeHelper

from .serializers import UserSerializer


class AuthUserDetailsView(RetrieveAPIView):
    """Get User Details, for auth purposes"""

    serializer_class = UserSerializer

    def get_object(self):
        return self.request.user

    def get_queryset(self):
        return User.objects.none()

    def get(self, request, *args, **kwargs):
        user = self.request.user

        timezone = request.headers.get("x-timezone")
        if timezone and not user.timezone:
            user.timezone = timezone
            user.save()

        if user.needs_initialization:
            user.initialize_user()
            user.needs_initialization = False
            user.save()

        # Sync the subscription from Stripe to our database
        stripe_helper = StripeHelper(self.request.user)
        stripe_helper.sync_subscription()

        return super().get(request, *args, **kwargs)
