#!/bin/bash

#
# The goal of this script is to be used when starting work on a new branch.
# It allows you to create a fresh database, and not have to worry about any
# migrations that may have been branch specific in your other branches.
# Once it creates the new database, it prompts you to create a new super user.
# Following that it will prompt you to create the first regular user.
# It will also automatically update your .env_dev to comment all existing DATABASE_URL
# and add a new fresh one that points to your new database that was created
#
# For example you can keep your databases straight by naming them "bicXXXX"
# where XXXX is the Jira Issue number related to your branch. Database names
# must start with a letter.
#
# If you want to go back to working on an old branch. You can checkout that branch
#  and then uncomment your .env_dev DATABASE_URL and pick back up in that branch.
# No need to run this script again
#
# You will want to run this script from the top level of the main repository like so


# init_new_database.sh
#
# If you don't it will run into issues

DJANGO_CONTAINER_NAME="shiptbot-django-1"

wait_for_docker_boot () {
    # Wait until the container is up
    #  the print command won't work until the docker container is up
    docker exec -it ${DJANGO_CONTAINER_NAME} python -c "print('Docker Container Started')"
    while [ $? -ne 0 ]; do docker exec -it ${DJANGO_CONTAINER_NAME} python -c "print('Docker Container Started')"; done
}

if [[ -z "${LOCAL_POSTGRES_PASS}" ]]; then
    echo "Environment variable LOCAL_POSTGRES_PASS must exist in your environment to run this"
    echo "export LOCAL_POSTGRES_PASS=<your desired default password>"
    exit 1
fi

if [ ! -f .env_dev ]; then
    echo "You must run this command from the top level of the repository. .env_dev should be at the level you run this command"
    exit 1
fi

# Export some variables we will use
export DJANGO_PG_NAME="shiptbot-db-1"
export DOCKER_COMPOSE_CURRENT=dev.yml

# Bring the docker container down if it is currently up
docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} down

# Get the name of the new database they want to create
read -p 'What do you want to name your database? ' DATABASE_NAME

# Bring up our docker container
docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} up -d

# Wait for docker container to boot
wait_for_docker_boot

# Create the database now that container is up
docker exec -it ${DJANGO_PG_NAME} psql postgres://postgres:${LOCAL_POSTGRES_PASS}@db:5432 -c "CREATE DATABASE ${DATABASE_NAME};"

# Check to make sure the database was made successfully
#  if not exit the script
if [ $? -ne 0 ]
then
    echo "Could not create ${DATABASE_NAME}. Exiting Docker"
    echo
    docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} down
    exit 1
fi

# Comment out the existing database URLs in .env_dev
sed -i "" -e "s/^DATABASE_URL/#DATABASE_URL/g" ".env_dev"

# Set the DATABASE_URL to the new one we just created
echo "DATABASE_URL=postgres://postgres:${LOCAL_POSTGRES_PASS}@db:5432/${DATABASE_NAME}" >> .env_dev

# Restart the container
docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} restart

# Again wait for the container to come back up
wait_for_docker_boot

echo "Makemigrations"
docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} exec django python manage.py makemigrations
# Run the migrations. Docker sometimes runs this migrate on start up. They may fight a bit and one
# will die and one won't. Either way it is fine.
echo "Migrate"
docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} exec django python manage.py migrate

# Create a super user for admin access
echo "Create A Superuser"
docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} exec django python manage.py createsuperuser

# Create the first user you will login with
# docker compose -f docker-compose.yml -f compose/${DOCKER_COMPOSE_CURRENT} exec django python manage.py create_test_account
