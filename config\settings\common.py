"""
Django settings for shiptbot project.

Generated by 'django-admin startproject' using Django 5.0.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

import logging
import os
from pathlib import Path

import environ
import sentry_sdk
from corsheaders.defaults import default_headers
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

ROOT_DIR = (
    environ.Path(__file__) - 3
)  # (/a/b/c/myfile.py - 3 = /a/), aka <repo>/shiptbot/
APPS_DIR = ROOT_DIR.path("shiptbot")
env = environ.Env()

# If DJANGO_NO_DOTENV is True, then no .env file read, and instead we rely
# only on actual environment settings in, e.g. the Docker container
READ_DOTENV = env.bool("DJANGO_READ_DOTENV", default=False)
DOTENV_FILE = env("DJANGO_DOTENV_FILE", default=".env")

if READ_DOTENV and os.path.isfile(ROOT_DIR(DOTENV_FILE)):
    env.read_env(ROOT_DIR(DOTENV_FILE))


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-9a8mvhx_0kwo$xd-2=k02bb$-b#un@jf+3#$b9#$s77kf^mbl#"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool("DJANGO_DEBUG", default=False)
DEBUG_REQUESTS = env.bool("DJANGO_DEBUG_REQUESTS", default=False)

# Application definition

DJANGO_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
]

THIRD_PARTY_APPS = [
    "rest_framework",
    "django_extensions",
    "corsheaders",
    "django_celery_beat",
]

LOCAL_APPS = [
    "shiptbot",
    "shiptbot.models.appconfig.apps.AppConfigConfig",
    "shiptbot.models.user.apps.UserConfig",
    "shiptbot.services.permissions.apps.AutodiscoverPermissionsConfig",
    "shiptbot.services.stytch.apps.StytchConfig",
    "shiptbot.util.apps.UtilConfig",
    "shiptbot.taskapp.celery.CeleryConfig",
    "shiptbot.models.bot.apps.BotConfig",
    "shiptbot.services.bot.apps.BotServiceConfig",
    "shiptbot.models.subscription.apps.SubscriptionConfig",
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "corsheaders.middleware.CorsMiddleware",
]

ROOT_URLCONF = "shiptbot.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "shiptbot.wsgi.application"


# DATABASE CONFIGURATION
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases
database_url = env.db("AWS_DATABASE_URL", default="")

if not database_url:
    database_url = env.db("DATABASE_URL", default="postgres:///shiptbot")
DATABASES = {
    "default": database_url,
}

# Turn this off to match production
# DATABASES["default"]["ATOMIC_REQUESTS"] = False


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = env("DJANGO_STATIC_URL", default="/static/")

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Select the correct user model
AUTH_USER_MODEL = "user.User"

STATIC_ROOT = ROOT_DIR("staticfiles")

STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
)

TEMPLATE_DIRS = (os.path.join(BASE_DIR, "templates"),)

# Django Rest Framework / Django Rest Auth
# ------------------------------------------------------------------------------
REST_USE_JWT = True

API_PAGE_SIZE = env.int("DJANGO_API_PAGE_SIZE", default=50)
API_MAX_PAGE_SIZE = env.int("DJANGO_API_MAX_PAGE_SIZE", default=100)

REST_FRAMEWORK = {
    # Each class is checked, and all permission checks must pass
    "DEFAULT_PERMISSION_CLASSES": (
        # request must be authenticated - via JWT or Token based auth
        "rest_framework.permissions.IsAuthenticated",
    ),
    # First class that authenticates will be used
    "DEFAULT_AUTHENTICATION_CLASSES": (
        # JWT based authentication - for User access API
        "shiptbot.services.permissions.authentication.ShiptBotAuthentication",
        # APIKey based authentication - for programmatic access to API
    ),
    # Disable the browsable API documentation by not including rest_framework.renderers.BrowsableAPIRenderer
    "DEFAULT_RENDERER_CLASSES": ("rest_framework.renderers.JSONRenderer",),
    # Note that filters still need to be added per view
    "DEFAULT_FILTER_BACKENDS": (
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.OrderingFilter",
        "rest_framework.filters.SearchFilter",
    ),
    "EXCEPTION_HANDLER": "shiptbot.util.handlers.blueinkvault_exception_handler",
}

# MEDIA CONFIGURATION
# ------------------------------------------------------------------------------
# See: https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = APPS_DIR("media")
TEST_DATA_PATH = "testdata/"
TEST_DATA_DIR = os.path.join(MEDIA_ROOT, TEST_DATA_PATH)

SAMPLE_DATA_PATH = "sampledata/"
SAMPLE_DATA_DIR = os.path.join(MEDIA_ROOT, SAMPLE_DATA_PATH)

# See: https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/media/"

# URL Configuration
# ------------------------------------------------------------------------------
ROOT_URLCONF = "config.urls"
APPEND_SLASH = False

# CORS Configuration
# ------------------------------------------------------------------------------
CORS_ALLOW_HEADERS = list(default_headers) + [
    "x-timezone",
]

# Django Admin
# ------------------------------------------------------------------------------
ADMIN_URL = env("DJANGO_ADMIN_URL", default="^admin/")
URL_BASE = env("DJANGO_URL_BASE", default="http://localhost:8000")

# STYTCH CONFIGURATION
# ------------------------------------------------------------------------------
STYTCH_PROJECT_ID = env.str("DJANGO_STYTCH_PROJECT_ID", default="")
STYTCH_SECRET = env.str("DJANGO_STYTCH_SECRET", default="")
STYTCH_PUBLIC_TOKEN = env.str("DJANGO_STYTCH_PROJECT_ID", default="")
STYTCH_JWT_ISSUER = env.str("DJANGO_STYTCH_JWT_ISSUER", default="")
STYTCH_AUTH_HEADER_PREFIX = env.str(
    "DJANGO_STYTCH_AUTH_HEADER_PREFIX", default="Bearer"
)
STYTCH_CACHE_TIMEOUT_SECS = env.int("DJANGO_STYTCH_CACHE_TIMEOUT_SECS", default=600)

# Redis Configuration
# ------------------------------------------------------------------------------
_redis_url_raw = env("REDIS_URL", default="")
REDIS_USE_SSL = env.bool("REDIS_USE_SSL", default=False)
REDIS_URL = ""
REDIS_URL_DB = ""
REDIS_URL_DB_CELERY = ""
REDIS_REQUIRE_CERTS = env.bool("REDIS_REQUIRE_CERTS", default=False)

# Celery Configuration
# ------------------------------------------------------------------------------

CELERY_BEAT_SCHEDULER = env(
    "CELERY_BEAT_SCHEDULER", default="django_celery_beat.schedulers:DatabaseScheduler"
)

# For development, you might want these to be True
CELERY_TASK_ALWAYS_EAGER = env.bool("CELERY_TASK_ALWAYS_EAGER", default=False)
CELERY_EAGER_PROPAGATES_EXCEPTIONS = env.bool(
    "CELERY_EAGER_PROPAGATES_EXCEPTIONS ", default=False
)
CELERY_USE_REDIS = env.bool("CELERY_USE_REDIS", default=False)
CELERY_USE_RESULT_BACKEND = env.bool("CELERY_USE_RESULT_BACKEND", default=False)
CELERY_BROKER_POOL_LIMIT = env.int("CELERY_BROKER_POOL_LIMIT", default=10)

CELERY_QUEUES = {
    "priority_queue": {
        "exchange": "priority_queue",
        "exchange_type": "direct",
        "binding_key": "priority_queue",
    },
    "default": {
        "exchange": "default",
        "exchange_type": "direct",
        "binding_key": "default",
    },
}

# Task routing configuration
CELERY_TASK_ROUTES = {
    "shiptbot.taskapp.tasks.priority_task": {"queue": "priority_queue"},
    "shiptbot.taskapp.tasks.default_task": {"queue": "default"},
}

CELERY_TASK_DEFAULT_QUEUE = "default"

if CELERY_USE_REDIS and REDIS_URL:
    CELERY_BROKER_URL = REDIS_URL
    # See https://docs.celeryproject.org/en/latest/getting-started/brokers/redis.html#caveats
    CELERY_BROKER_TRANSPORT_OPTIONS = {
        "fanout_prefix": True,
        "fanout_patterns": True,
        # Visibility Timeout should be as large as our max retry interval. See hooks.tasks
        "visibility_timeout": 19440,
        "max_connections": env.int("CELERY_BROKER_MAX_CONNECTIONS", default=20),
    }
else:
    CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="redis://")

if CELERY_USE_RESULT_BACKEND:
    CELERY_RESULT_BACKEND = env("CELERY_RESULT_BACKEND", default="")
    if not CELERY_RESULT_BACKEND and CELERY_USE_REDIS and REDIS_URL_DB_CELERY:
        CELERY_RESULT_BACKEND = REDIS_URL_DB_CELERY
        # This setting is used for the results backend, it should be set FIXME
        # https://docs.celeryproject.org/en/stable/userguide/configuration.html#redis-max-connections
        # CELERY_REDIS_MAX_CONNECTIONS

# Add additional imports that will not be autodiscovered, because they are not Django apps
CELERY_IMPORTS = []


# Sentry
# ------------------------------------------------------------------------------
ENABLE_SENTRY = bool(env("DJANGO_SENTRY_DSN", default=None))
SENTRY_TRACES_SAMPLE_RATE = env.float("DJANGO_SENTRY_TRACES_SAMPLE_RATE", default=0.01)
SENTRY_PROFILES_SAMPLE_RATE = env.float(
    "DJANGO_SENTRY_PROFILES_SAMPLE_RATE", default=0.01
)

if ENABLE_SENTRY:
    # SENTRY_CONFIG = {
    #     "dsn": env("DJANGO_SENTRY_DSN"),
    #     "CELERY_LOGLEVEL": env.int("DJANGO_SENTRY_LOG_LEVEL", default=logging.INFO),
    # }
    sentry_sdk.init(
        dsn=env("DJANGO_SENTRY_DSN"),
        integrations=[
            DjangoIntegration(),
            LoggingIntegration(event_level=logging.WARNING),
        ],
        # Add data like request headers and IP for users;
        # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=SENTRY_TRACES_SAMPLE_RATE,
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=SENTRY_PROFILES_SAMPLE_RATE,
    )

# By convention, ENVs are: production, local, review, feature, sandbox, staging
ENV_LABEL = env("DJANGO_ENV_LABEL", default="")


# Stripe
# ------------------------------------------------------------------------------
STRIPE_SECRET_KEY = env("DJANGO_STRIPE_SECRET_KEY", default="")
STRIPE_CHECKOUT_SUCCESS_URL = env("DJANGO_STRIPE_CHECKOUT_SUCCESS_URL", default="")
STRIPE_BILLING_PORTAL_RETURN_URL = env(
    "DJANGO_STRIPE_BILLING_PORTAL_RETURN_URL", default=""
)

# Trial Days
# ------------------------------------------------------------------------------
TRIAL_DAYS = env.int("DJANGO_TRIAL_DAYS", default=7)


# Bot Settings
# ------------------------------------------------------------------------------
FAKE_CLAIM_OFFER = env.bool("DJANGO_FAKE_CLAIM_OFFER", default=True)

# Auth0 Shipt Settings
# ------------------------------------------------------------------------------
AUTH0_SHIPT_SECRET_KEY = env.str("DJANGO_AUTH0_SHIPT_SECRET_KEY", default="")

# Mailgun
# ------------------------------------------------------------------------------
MAILGUN_API_KEY = env("DJANGO_MAILGUN_API_KEY", default="")
MAILGUN_DOMAIN = env("DJANGO_MAILGUN_DOMAIN", default="")
MAILGUN_FROM_EMAIL = env(
    "DJANGO_MAILGUN_FROM_EMAIL", default="<EMAIL>"
)
