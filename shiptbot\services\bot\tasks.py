import arrow

from shiptbot.models.bot.models import Session
from shiptbot.models.user.models import User
from shiptbot.services.bot.shipt_bot import ShiptBot
from shiptbot.taskapp import celery_app
from shiptbot.taskapp.base import LoggingTask


@celery_app.task(base=LoggingTask, bind=True, max_retries=0)
def async_start_bot(self, user_id):
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        raise Exception("User does not exist")

    if not user.shipt_logged_in:
        raise Exception("User is not logged in")

    user.shipt_is_running = True
    user.save()

    session = Session.objects.create(user=user)

    shiptbot = ShiptBot(user, session)
    shiptbot.start_seaching()


@celery_app.task(base=LoggingTask, bind=True, max_retries=0)
def async_start_scheduled_search(self):
    now = arrow.utcnow()
