import React from 'react'
import {
  <PERSON>,
  CardContent,
  Box,
  Button,
  Typography,
  CircularProgress
} from '@mui/material'
import { styled } from '@mui/material/styles'
import SearchIcon from '@mui/icons-material/Search'
import StopIcon from '@mui/icons-material/Stop'
import carLoadingGif from '../../../assets/images/car-loading.gif'

const SearchContainer = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  borderRadius: 12,
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
  border: '1px solid #e0e0e0'
}))

const SearchContent = styled(CardContent)(({ theme }) => ({
  padding: theme.spacing(2),
  '&:last-child': {
    paddingBottom: theme.spacing(2)
  }
}))

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '16px',
  gap: '8px',
  backgroundColor: '#ffffff',
  borderRadius: 8,
  border: '2px dashed',
  borderColor: theme.palette.primary.main,
  animation: 'blinkBorder 1.5s ease-in-out infinite',
  '@keyframes blinkBorder': {
    '0%': {
      borderColor: theme.palette.primary.main,
      boxShadow: `0 0 0 0 ${theme.palette.primary.main}40`
    },
    '50%': {
      borderColor: theme.palette.primary.light,
      boxShadow: `0 0 0 4px ${theme.palette.primary.main}20`
    },
    '100%': {
      borderColor: theme.palette.primary.main,
      boxShadow: `0 0 0 0 ${theme.palette.primary.main}40`
    }
  }
}))

const CarImage = styled('img')({
  width: '110px',
  maxWidth: 300,
  height: 'auto'
})

const OffersSearch = ({ onSearchStart, onSearchStop, isSearching = false, isLoading = false }) => {
  const handleStartSearch = () => {
    if (onSearchStart) {
      onSearchStart()
    }
  }

  const handleStopSearch = () => {
    if (onSearchStop) {
      onSearchStop()
    }
  }

  return (
    <SearchContainer>
      <SearchContent>
        {!isSearching ? (
          <Box sx={{ textAlign: 'center' }}>
            <Button
              variant='contained'
              size='large'
              startIcon={isLoading ? <CircularProgress size={20} color='inherit' /> : <SearchIcon />}
              onClick={handleStartSearch}
              disabled={isLoading}
              sx={{
                width: { xs: '100%', sm: '100%', md: '100%' },
                px: 4,
                py: 1.5,
                fontSize: { xs: '0.9rem', sm: '1rem' },
                fontWeight: 600,
                borderRadius: 2,
                textTransform: 'none',
                boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
                  transform: 'translateY(-1px)'
                },
                '&:disabled': {
                  boxShadow: '0 2px 4px rgba(25, 118, 210, 0.2)',
                  transform: 'none'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              {isLoading ? 'Connecting to Server...' : 'Start Searching'}
            </Button>
          </Box>
        ) : (
          // Searching State
          <LoadingContainer>
            <CarImage src={carLoadingGif} alt='Searching for offers...' />
            <Typography
              variant='body2'
              color='text.secondary'
              sx={{
                fontSize: { xs: '0.85rem', sm: '0.9rem' },
                textAlign: 'center',
                mb: '2px',
                display: { xs: 'none', sm: 'block' }
              }}
            >
              We're looking for the best delivery offers in your area
            </Typography>
            <Button
              variant='outlined'
              size='medium'
              startIcon={<StopIcon />}
              onClick={handleStopSearch}
              sx={{
                px: 3,
                py: 1,
                fontSize: { xs: '0.85rem', sm: '0.9rem' },
                fontWeight: 500,
                borderRadius: 2,
                textTransform: 'none',
                borderColor: 'error.main',
                color: 'error.main',
                '&:hover': {
                  borderColor: 'error.dark',
                  backgroundColor: 'error.light',
                  color: 'error.dark'
                }
              }}
            >
              Stop
            </Button>
          </LoadingContainer>
        )}
      </SearchContent>
    </SearchContainer>
  )
}

export default OffersSearch
